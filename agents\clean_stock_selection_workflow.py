import asyncio
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import polars as pl
import yaml
from datetime import datetime, timedelta
import time

# Import required agents
from agents.live_data_management_agent import LiveDataManagementAgent
from agents.live_feature_engineering_agent import LiveFeatureEngineeringAgent
from agents.live_ml_prediction_agent import LiveMLPredictionAgent
from agents.live_stock_selection_agent import LiveStockSelectionAgent
from agents.live_strategy_assignment_agent import LiveStrategyAssignmentAgent

logger = logging.getLogger(__name__)

class CleanWorkflowResult:
    """Clean data class to hold the results of the workflow execution."""
    def __init__(self):
        self.selected_stocks: List[str] = []
        self.strategy_assignments: Dict[str, Any] = {}
        self.reports: Dict[str, Any] = {}
        self.warnings: List[str] = []
        self.recommendations: List[str] = []
        self.execution_summary: Dict[str, Any] = {}
        self.historical_data_paths: Dict[str, str] = {}  # Symbol -> file path mapping
        self.timeframe_data_paths: Dict[str, Dict[str, str]] = {}  # Symbol -> {timeframe -> file path}

class CleanStockSelectionWorkflow:
    """
    Clean, logical workflow for stock selection and strategy assignment.
    
    Flow:
    1. Download historical data (1-min for 25 days)
    2. Feature engineering on downloaded data
    3. ML prediction using trained models
    4. Stock selection (top 50 stocks)
    5. Generate higher timeframes (3min, 5min, 15min) from 1-min data
    6. Prepare for websocket subscription and trading agents initialization
    """
    
    def __init__(self, event_bus: Any, session_id: str, config_path: Optional[Path] = None):
        self.config_path = config_path if config_path else Path(__file__).parent.parent / "config" / "live_stock_selection_config.yaml"
        self.config: Dict[str, Any] = {}
        self.agents: Dict[str, Any] = {}
        self.workflow_result = CleanWorkflowResult()
        self.event_bus = event_bus
        self.session_id = session_id
        self.start_time: Optional[datetime] = None
        
        # Data paths
        self.project_root = Path(__file__).parent.parent
        self.live_data_path = self.project_root / "data" / "live"
        self.feature_data_path = self.project_root / "data" / "features"
        
        # Ensure directories exist
        self.live_data_path.mkdir(parents=True, exist_ok=True)
        self.feature_data_path.mkdir(parents=True, exist_ok=True)

    async def initialize(self) -> bool:
        """Initialize the workflow by loading configuration and setting up agents."""
        logger.info("[CLEAN-WORKFLOW] Initializing Clean Stock Selection Workflow...")
        try:
            self._load_config()
            self._setup_agents()
            await self._initialize_agents()
            
            logger.info("[CLEAN-WORKFLOW] Clean Stock Selection Workflow initialized successfully.")
            return True
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Initialization failed: {e}", exc_info=True)
            return False

    def _load_config(self):
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        logger.info(f"[CLEAN-WORKFLOW] Configuration loaded from {self.config_path}")

    def _setup_agents(self):
        """Set up instances of all required agents."""
        self.agents['data_management'] = LiveDataManagementAgent(
            event_bus=self.event_bus,
            session_id=self.session_id
        )
        self.agents['feature_engineering'] = LiveFeatureEngineeringAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['ml_prediction'] = LiveMLPredictionAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['stock_selection'] = LiveStockSelectionAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        self.agents['strategy_assignment'] = LiveStrategyAssignmentAgent(
            event_bus=self.event_bus,
            config=self.config,
            session_id=self.session_id
        )
        logger.info("[CLEAN-WORKFLOW] All agents instantiated.")

    async def _initialize_agents(self):
        """Initialize all agents that require async initialization."""
        logger.info("[CLEAN-WORKFLOW] Initializing agents...")
        
        for agent_name, agent in self.agents.items():
            if hasattr(agent, 'initialize'):
                try:
                    logger.info(f"[CLEAN-WORKFLOW] Initializing {agent_name} agent...")
                    success = await agent.initialize()
                    if success:
                        logger.info(f"[CLEAN-WORKFLOW] ✓ {agent_name} agent initialized successfully")
                    else:
                        logger.warning(f"[CLEAN-WORKFLOW] ⚠️ {agent_name} agent initialization returned False")
                except Exception as e:
                    logger.error(f"[CLEAN-WORKFLOW] ❌ Failed to initialize {agent_name} agent: {e}")
                    raise
        
        logger.info("[CLEAN-WORKFLOW] All agents initialized.")

    async def execute_workflow(self, stocks_to_process: List[str]) -> CleanWorkflowResult:
        """
        Execute the clean stock selection workflow.
        
        Args:
            stocks_to_process: List of stock symbols to analyze
            
        Returns:
            CleanWorkflowResult with selected stocks and metadata
        """
        self.start_time = datetime.now()
        logger.info(f"[CLEAN-WORKFLOW] Starting clean workflow execution for {len(stocks_to_process)} stocks...")
        self.workflow_result = CleanWorkflowResult()  # Reset results

        try:
            # Step 1: Download Historical Data (1-min for 25 days)
            logger.info("[CLEAN-WORKFLOW] Step 1: Downloading historical data...")
            downloaded_data = await self._download_historical_data(stocks_to_process)
            
            if not downloaded_data:
                logger.error("[CLEAN-WORKFLOW] No historical data downloaded. Cannot proceed.")
                self.workflow_result.warnings.append("No historical data downloaded")
                return self.workflow_result
            
            logger.info(f"[CLEAN-WORKFLOW] Downloaded data for {len(downloaded_data)} stocks")
            self.workflow_result.historical_data_paths = downloaded_data

            # Step 2: Feature Engineering
            logger.info("[CLEAN-WORKFLOW] Step 2: Performing feature engineering...")
            features_dict = await self._perform_feature_engineering(downloaded_data)
            
            if not features_dict:
                logger.error("[CLEAN-WORKFLOW] Feature engineering failed. Cannot proceed.")
                self.workflow_result.warnings.append("Feature engineering failed")
                return self.workflow_result
            
            logger.info(f"[CLEAN-WORKFLOW] Engineered features for {len(features_dict)} stocks")

            # Step 3: ML Prediction
            logger.info("[CLEAN-WORKFLOW] Step 3: Generating ML predictions...")
            predictions_dict = await self._generate_ml_predictions(features_dict)
            
            if not predictions_dict:
                logger.error("[CLEAN-WORKFLOW] ML prediction failed. Cannot proceed.")
                self.workflow_result.warnings.append("ML prediction failed")
                return self.workflow_result
            
            logger.info(f"[CLEAN-WORKFLOW] Generated predictions for {len(predictions_dict)} stocks")

            # Step 4: Stock Selection (Top 50)
            logger.info("[CLEAN-WORKFLOW] Step 4: Selecting top 50 stocks...")
            selected_stocks = await self._select_top_stocks(predictions_dict)
            
            if not selected_stocks:
                logger.error("[CLEAN-WORKFLOW] Stock selection failed. Cannot proceed.")
                self.workflow_result.warnings.append("Stock selection failed")
                return self.workflow_result
            
            logger.info(f"[CLEAN-WORKFLOW] Selected {len(selected_stocks)} stocks")
            self.workflow_result.selected_stocks = selected_stocks

            # Step 5: Generate Higher Timeframes (3min, 5min, 15min)
            logger.info("[CLEAN-WORKFLOW] Step 5: Generating higher timeframes...")
            timeframe_data = await self._generate_higher_timeframes(selected_stocks)
            self.workflow_result.timeframe_data_paths = timeframe_data
            
            logger.info(f"[CLEAN-WORKFLOW] Generated timeframe data for {len(timeframe_data)} stocks")

            # Step 6: Strategy Assignment
            logger.info("[CLEAN-WORKFLOW] Step 6: Assigning strategies...")
            strategy_assignments = await self._assign_strategies(selected_stocks, predictions_dict, features_dict)
            self.workflow_result.strategy_assignments = strategy_assignments
            
            # Log strategy assignment results properly
            if hasattr(strategy_assignments, 'assignments'):
                logger.info(f"[CLEAN-WORKFLOW] Assigned strategies for {len(strategy_assignments.assignments)} stocks")
            else:
                logger.info(f"[CLEAN-WORKFLOW] Strategy assignment completed")

            # Generate reports
            self._generate_reports()
            
            logger.info("[CLEAN-WORKFLOW] ✅ Workflow completed successfully!")
            
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Workflow execution failed: {e}", exc_info=True)
            self.workflow_result.warnings.append(f"Workflow execution failed: {e}")
        finally:
            self._finalize_execution_summary()
            
        return self.workflow_result

    async def _download_historical_data(self, stocks: List[str]) -> Dict[str, str]:
        """
        Download 25 days of 1-minute historical data for given stocks with retry mechanism.
        
        Returns:
            Dict mapping symbol to file path of downloaded data
        """
        logger.info(f"[CLEAN-WORKFLOW] Downloading historical data for {len(stocks)} stocks...")
        
        downloaded_data = {}
        failed_stocks = []
        
        try:
            # Use the data management agent to download data with reasonable timeout
            data_paths = await asyncio.wait_for(
                self.agents['data_management'].download_historical_data(stocks),
                timeout=600.0  # 10 minute timeout for all stocks
            )
            
            if data_paths:
                # Save data to live folder and track file paths
                for symbol, data_df in data_paths.items():
                    if data_df is not None and not data_df.is_empty():
                        file_path = self.live_data_path / f"{symbol}_1min.parquet"
                        data_df.write_parquet(file_path, compression='brotli')
                        downloaded_data[symbol] = str(file_path)
                        logger.info(f"[CLEAN-WORKFLOW] Saved 1-min data for {symbol} ({len(data_df)} rows)")
                    else:
                        logger.warning(f"[CLEAN-WORKFLOW] No data received for {symbol}")
                        failed_stocks.append(symbol)
            
            # Retry failed stocks if any
            if failed_stocks and len(failed_stocks) < len(stocks) * 0.5:  # Only retry if less than 50% failed
                logger.info(f"[CLEAN-WORKFLOW] Retrying {len(failed_stocks)} failed downloads...")
                retry_data_paths = await asyncio.wait_for(
                    self.agents['data_management'].download_historical_data(failed_stocks),
                    timeout=300.0  # 5 minute timeout for retries
                )
                
                if retry_data_paths:
                    for symbol, data_df in retry_data_paths.items():
                        if data_df is not None and not data_df.is_empty():
                            file_path = self.live_data_path / f"{symbol}_1min.parquet"
                            data_df.write_parquet(file_path, compression='brotli')
                            downloaded_data[symbol] = str(file_path)
                            logger.info(f"[CLEAN-WORKFLOW] Retry successful for {symbol} ({len(data_df)} rows)")
            
        except asyncio.TimeoutError:
            logger.error("[CLEAN-WORKFLOW] Historical data download timed out")
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error downloading historical data: {e}")
        
        logger.info(f"[CLEAN-WORKFLOW] Successfully downloaded data for {len(downloaded_data)}/{len(stocks)} stocks")
        return downloaded_data

    async def _perform_feature_engineering(self, downloaded_data: Dict[str, str]) -> Dict[str, Any]:
        """
        Perform feature engineering on downloaded data.
        
        Args:
            downloaded_data: Dict mapping symbol to file path
            
        Returns:
            Dict mapping symbol to feature engineering results
        """
        logger.info(f"[CLEAN-WORKFLOW] Performing feature engineering for {len(downloaded_data)} stocks...")
        
        # Load data and prepare for feature engineering
        data_dict = {}
        for symbol, file_path in downloaded_data.items():
            try:
                data_df = pl.read_parquet(file_path)
                data_dict[symbol] = data_df
            except Exception as e:
                logger.error(f"[CLEAN-WORKFLOW] Error loading data for {symbol}: {e}")
        
        if not data_dict:
            logger.error("[CLEAN-WORKFLOW] No data loaded for feature engineering")
            return {}
        
        try:
            # Use feature engineering agent
            features_dict = await asyncio.wait_for(
                self.agents['feature_engineering'].calculate_features_for_stocks(data_dict),
                timeout=180.0  # 3 minute timeout for feature engineering
            )
            
            return features_dict if features_dict else {}
            
        except asyncio.TimeoutError:
            logger.error("[CLEAN-WORKFLOW] Feature engineering timed out")
            return {}
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error in feature engineering: {e}")
            return {}

    async def _generate_ml_predictions(self, features_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate ML predictions using trained models.
        
        Args:
            features_dict: Dict mapping symbol to feature engineering results
            
        Returns:
            Dict mapping symbol to prediction results
        """
        logger.info(f"[CLEAN-WORKFLOW] Generating ML predictions for {len(features_dict)} stocks...")
        
        # Convert features to DataFrame format expected by ML agent
        features_df = self._convert_features_to_dataframe(features_dict)
        
        if features_df.is_empty():
            logger.error("[CLEAN-WORKFLOW] No features available for ML prediction")
            return {}
        
        try:
            # Use ML prediction agent
            predictions_dict = await asyncio.wait_for(
                self.agents['ml_prediction'].generate_predictions(features_df),
                timeout=180.0  # 3 minute timeout for ML predictions
            )
            
            return predictions_dict if predictions_dict else {}
            
        except asyncio.TimeoutError:
            logger.error("[CLEAN-WORKFLOW] ML prediction timed out")
            return {}
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error in ML prediction: {e}")
            return {}

    async def _select_top_stocks(self, predictions_dict: Dict[str, Any]) -> List[str]:
        """
        Select top 50 stocks based on ML predictions.
        
        Args:
            predictions_dict: Dict mapping symbol to prediction results
            
        Returns:
            List of selected stock symbols
        """
        logger.info(f"[CLEAN-WORKFLOW] Selecting top stocks from {len(predictions_dict)} predictions...")
        
        try:
            # Create dummy quality metrics for stock selection
            quality_metrics = {}
            for symbol in predictions_dict.keys():
                quality_metrics[symbol] = type('QualityMetrics', (), {
                    'is_valid': True, 
                    'quality_score': 0.8
                })()
            
            # Use stock selection agent
            selection_result = await asyncio.wait_for(
                self.agents['stock_selection'].select_stocks(predictions_dict, quality_metrics),
                timeout=60.0  # 1 minute timeout
            )
            
            if selection_result and hasattr(selection_result, 'selected_stocks'):
                # Extract stock symbols from selection result
                selected_stocks = []
                for item in selection_result.selected_stocks:
                    if isinstance(item, dict) and 'symbol' in item:
                        selected_stocks.append(item['symbol'])
                    elif isinstance(item, str):
                        selected_stocks.append(item)
                    else:
                        selected_stocks.append(str(item))
                
                # Limit to top 50 stocks
                return selected_stocks[:50]
            
            return []
            
        except asyncio.TimeoutError:
            logger.error("[CLEAN-WORKFLOW] Stock selection timed out")
            return []
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error in stock selection: {e}")
            return []

    async def _generate_higher_timeframes(self, selected_stocks: List[str]) -> Dict[str, Dict[str, str]]:
        """
        Generate 3min, 5min, and 15min data from 1min data for selected stocks.
        
        Args:
            selected_stocks: List of selected stock symbols
            
        Returns:
            Dict mapping symbol to dict of timeframe -> file path
        """
        logger.info(f"[CLEAN-WORKFLOW] Generating higher timeframes for {len(selected_stocks)} stocks...")
        
        timeframe_data = {}
        timeframes = {'3min': 3, '5min': 5, '15min': 15}
        
        for symbol in selected_stocks:
            timeframe_data[symbol] = {}
            
            # Load 1-min data
            input_file = self.live_data_path / f"{symbol}_1min.parquet"
            if not input_file.exists():
                logger.warning(f"[CLEAN-WORKFLOW] 1-min data not found for {symbol}")
                continue
            
            try:
                df_1min = pl.read_parquet(input_file)
                
                for tf_name, minutes in timeframes.items():
                    try:
                        # Group by time intervals and aggregate OHLCV data
                        df_tf = df_1min.group_by_dynamic(
                            "timestamp",
                            every=f"{minutes}m",
                            closed="left"
                        ).agg([
                            pl.col("open").first().alias("open"),
                            pl.col("high").max().alias("high"),
                            pl.col("low").min().alias("low"),
                            pl.col("close").last().alias("close"),
                            pl.col("volume").sum().alias("volume")
                        ]).sort("timestamp")
                        
                        # Remove any rows with null values
                        df_tf = df_tf.drop_nulls()
                        
                        if not df_tf.is_empty():
                            output_file = self.live_data_path / f"{symbol}_{tf_name}.parquet"
                            df_tf.write_parquet(output_file, compression='brotli')
                            timeframe_data[symbol][tf_name] = str(output_file)
                            logger.debug(f"[CLEAN-WORKFLOW] Generated {tf_name} data for {symbol} ({len(df_tf)} rows)")
                        else:
                            logger.warning(f"[CLEAN-WORKFLOW] No {tf_name} data generated for {symbol}")
                            
                    except Exception as e:
                        logger.error(f"[CLEAN-WORKFLOW] Error generating {tf_name} data for {symbol}: {e}")
                        
            except Exception as e:
                logger.error(f"[CLEAN-WORKFLOW] Error processing {symbol}: {e}")
        
        return timeframe_data

    async def _assign_strategies(self, selected_stocks: List[str], predictions_dict: Dict[str, Any], features_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Assign trading strategies to selected stocks.
        
        Args:
            selected_stocks: List of selected stock symbols
            predictions_dict: Dict mapping symbol to prediction results
            features_dict: Dict mapping symbol to feature engineering results
            
        Returns:
            Dict mapping symbol to strategy assignment
        """
        logger.info(f"[CLEAN-WORKFLOW] Assigning strategies for {len(selected_stocks)} stocks...")
        
        try:
            # Prepare selection result format for strategy assignment
            selection_result_items = []
            for symbol in selected_stocks:
                selection_result_items.append({'symbol': symbol, 'score': 0.8})
            
            # Use strategy assignment agent
            strategy_assignments = await asyncio.wait_for(
                self.agents['strategy_assignment'].assign_strategies(
                    selected_stocks,  # Pass list of symbols, not dict objects
                    predictions_dict,
                    features_dict
                ),
                timeout=60.0  # 1 minute timeout
            )
            
            return strategy_assignments if strategy_assignments else {}
            
        except asyncio.TimeoutError:
            logger.error("[CLEAN-WORKFLOW] Strategy assignment timed out")
            return {}
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error in strategy assignment: {e}")
            return {}

    def _convert_features_to_dataframe(self, features_dict: Dict[str, Any]) -> pl.DataFrame:
        """Convert features dictionary to Polars DataFrame for ML prediction."""
        try:
            rows = []
            for symbol, feature_eng in features_dict.items():
                if hasattr(feature_eng, 'is_valid') and feature_eng.is_valid:
                    row = {'symbol': symbol}
                    if hasattr(feature_eng, 'features'):
                        row.update(feature_eng.features)
                    if hasattr(feature_eng, 'target_return') and feature_eng.target_return is not None:
                        row['target_return'] = feature_eng.target_return
                    rows.append(row)
            
            if not rows:
                logger.warning("[CLEAN-WORKFLOW] No valid features to convert to DataFrame")
                return pl.DataFrame()
            
            features_df = pl.DataFrame(rows)
            logger.info(f"[CLEAN-WORKFLOW] Converted {len(rows)} feature records to DataFrame")
            return features_df
            
        except Exception as e:
            logger.error(f"[CLEAN-WORKFLOW] Error converting features to DataFrame: {e}")
            return pl.DataFrame()

    def _generate_reports(self):
        """Generate comprehensive reports based on workflow results."""
        logger.info("[CLEAN-WORKFLOW] Generating reports...")
        
        # Selected stocks report
        self.workflow_result.reports['selected_stocks'] = {
            'count': len(self.workflow_result.selected_stocks),
            'symbols': self.workflow_result.selected_stocks,
            'data_files': self.workflow_result.timeframe_data_paths
        }
        
        # Strategy assignments report
        self.workflow_result.reports['strategy_assignments'] = self.workflow_result.strategy_assignments
        
        # Data quality report
        self.workflow_result.reports['data_quality'] = {
            'historical_data_count': len(self.workflow_result.historical_data_paths),
            'timeframe_data_count': len(self.workflow_result.timeframe_data_paths)
        }
        
        logger.info("[CLEAN-WORKFLOW] Reports generated.")

    def _finalize_execution_summary(self):
        """Calculate and store execution summary."""
        if self.start_time:
            end_time = datetime.now()
            total_time = (end_time - self.start_time).total_seconds()
            self.workflow_result.execution_summary = {
                'total_execution_time': total_time,
                'end_time': end_time.isoformat(),
                'status': 'Completed' if not self.workflow_result.warnings else 'Completed with Warnings',
                'selected_stocks_count': len(self.workflow_result.selected_stocks),
                'warnings_count': len(self.workflow_result.warnings)
            }
        
        logger.info(f"[CLEAN-WORKFLOW] Execution Summary: {self.workflow_result.execution_summary}")

    async def cleanup(self):
        """Clean up resources used by the workflow."""
        logger.info("[CLEAN-WORKFLOW] Cleaning up resources...")
        # Individual agents might have their own cleanup methods
        logger.info("[CLEAN-WORKFLOW] Cleanup complete.")

    def get_selected_stocks_for_websocket(self) -> List[str]:
        """
        Get the list of selected stocks ready for websocket subscription.
        
        Returns:
            List of stock symbols ready for live trading
        """
        return self.workflow_result.selected_stocks

    def get_timeframe_data_paths(self) -> Dict[str, Dict[str, str]]:
        """
        Get the paths to generated timeframe data files.
        
        Returns:
            Dict mapping symbol to dict of timeframe -> file path
        """
        return self.workflow_result.timeframe_data_paths