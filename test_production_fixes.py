#!/usr/bin/env python3
"""
Test script to verify production fixes:
1. No demo data fallback
2. Proper websocket handler exists
3. System status updates every 20 signal cycles
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from core.event_system import EventBus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockConfig:
    """Mock configuration for testing"""
    def __init__(self):
        self.selected_stocks = ["RELIANCE", "TCS", "HDFCBANK"]
        self.timeframes = ["1min", "5min"]

async def test_websocket_handler_exists():
    """Test that _websocket_handler method exists"""
    logger.info("Testing websocket handler existence...")
    
    event_bus = EventBus()
    config = MockConfig()
    
    agent = CleanMarketDataAgent(event_bus, config, "test_session")
    
    # Check if _websocket_handler method exists
    if hasattr(agent, '_websocket_handler'):
        logger.info("✅ _websocket_handler method exists")
        return True
    else:
        logger.error("❌ _websocket_handler method missing")
        return False

async def test_no_demo_data_fallback():
    """Test that demo data fallback is disabled"""
    logger.info("Testing demo data fallback behavior...")

    event_bus = EventBus()
    config = MockConfig()

    agent = CleanMarketDataAgent(event_bus, config, "test_session")

    # Initialize the agent
    await agent.initialize()

    # Start the agent - it should start but websocket handler should fail in background
    await agent.start()

    # Wait a moment for background tasks to execute
    await asyncio.sleep(2)

    # Check if websocket task failed (which is expected in production mode)
    if hasattr(agent, 'websocket_task') and agent.websocket_task:
        if agent.websocket_task.done() and agent.websocket_task.exception():
            exception = agent.websocket_task.exception()
            if "production mode" in str(exception).lower() or "real websocket" in str(exception).lower():
                logger.info("✅ Websocket handler properly fails in production mode without demo data fallback")
                return True
            else:
                logger.error(f"❌ Websocket failed for unexpected reason: {exception}")
                return False
        else:
            logger.error("❌ Websocket task did not fail as expected")
            return False
    else:
        logger.error("❌ No websocket task found")
        return False

async def test_signal_cycle_tracking():
    """Test signal cycle tracking for status updates"""
    logger.info("Testing signal cycle tracking...")
    
    event_bus = EventBus()
    config = MockConfig()
    
    signal_agent = CleanSignalAgent(event_bus, config, "test_session")
    
    # Check if loop_count attribute exists
    if hasattr(signal_agent, 'loop_count'):
        logger.info("✅ Signal cycle tracking (loop_count) exists")
        return True
    else:
        logger.error("❌ Signal cycle tracking (loop_count) missing")
        return False

async def main():
    """Run all tests"""
    logger.info("🧪 Starting production fixes verification tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Websocket Handler Exists", test_websocket_handler_exists),
        ("No Demo Data Fallback", test_no_demo_data_fallback),
        ("Signal Cycle Tracking", test_signal_cycle_tracking),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"Running test: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            logger.info(f"Test '{test_name}': {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))
        
        logger.info("-" * 40)
    
    # Summary
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All production fixes verified successfully!")
        return True
    else:
        logger.error("⚠️  Some tests failed - fixes may need adjustment")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
