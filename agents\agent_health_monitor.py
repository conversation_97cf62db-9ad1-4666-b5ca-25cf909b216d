#!/usr/bin/env python3
"""
Agent Health Monitoring System
=============================

Monitors the health and performance of all trading system agents,
automatically detects failures, and implements recovery mechanisms.

Features:
- Real-time agent health monitoring
- Automatic failure detection
- Agent restart and recovery
- Performance metrics tracking
- Health alerts and notifications
- Dependency management
"""

import asyncio
import logging
import json
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import threading

from .base_agent import BaseAgent
from utils.event_bus import EventBus, EventTypes

logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    """Agent status classifications"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    FAILED = "failed"
    RESTARTING = "restarting"
    STOPPED = "stopped"

@dataclass
class AgentHealth:
    """Agent health metrics"""
    agent_name: str
    status: AgentStatus
    last_heartbeat: datetime
    last_activity: datetime
    error_count: int = 0
    restart_count: int = 0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    response_time: float = 0.0
    uptime: float = 0.0
    performance_score: float = 1.0
    
@dataclass
class AgentDependency:
    """Agent dependency definition"""
    dependent_agent: str
    required_agent: str
    dependency_type: str  # 'hard' or 'soft'
    timeout_seconds: int = 30

class AgentHealthMonitor(BaseAgent):
    """
    Comprehensive agent health monitoring system
    
    Monitors all trading system agents and ensures they remain healthy
    and responsive. Automatically detects failures and implements
    recovery strategies.
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("AgentHealthMonitor", event_bus, config, session_id)
        
        # Health tracking
        self.agent_health = {}
        self.agent_dependencies = []
        self.failed_agents = set()
        self.restarting_agents = set()
        
        # Monitoring configuration
        self.heartbeat_timeout = 60  # seconds
        self.health_check_interval = 30  # seconds
        self.max_restart_attempts = 3
        self.restart_cooldown = 300  # 5 minutes
        
        # Performance tracking
        self.performance_history = {}
        self.system_metrics = {}
        
        # Recovery mechanisms
        self.auto_restart_enabled = True
        self.dependency_restart_enabled = True
        
        logger.info(f"[INIT] {self.name} initialized")
    
    async def start(self):
        """Start the health monitoring system"""
        try:
            await super().start()
            
            # Initialize agent dependencies
            self._initialize_dependencies()
            
            # Start monitoring loops
            asyncio.create_task(self._health_monitoring_loop())
            asyncio.create_task(self._performance_monitoring_loop())
            asyncio.create_task(self._system_monitoring_loop())
            asyncio.create_task(self._recovery_monitoring_loop())
            
            # Subscribe to events
            await self._subscribe_to_events()
            
            logger.info(f"[START] {self.name} started with health monitoring active")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            raise

    async def initialize(self):
        """Initialize the health monitor (required by BaseAgent)"""
        try:
            logger.info(f"[INIT] Initializing {self.name}")
            # Initialization is done in __init__, this is for BaseAgent compatibility
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize {self.name}: {e}")
            return False

    async def stop(self):
        """Stop the health monitor (required by BaseAgent)"""
        try:
            logger.info(f"[STOP] Stopping {self.name}")
            self.is_running = False
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop {self.name}: {e}")
            return False
    
    def _initialize_dependencies(self):
        """Initialize agent dependency mappings"""
        try:
            # Define critical dependencies
            self.agent_dependencies = [
                AgentDependency("SignalGenerationAgent", "CleanMarketDataAgent", "hard", 30),
                AgentDependency("ModernExecutionAgent", "CleanMarketDataAgent", "hard", 30),
                AgentDependency("ModernExecutionAgent", "RiskManagementAgent", "hard", 15),
                AgentDependency("RiskManagementAgent", "CleanMarketDataAgent", "soft", 60),
                AgentDependency("MarketMonitoringAgent", "CleanMarketDataAgent", "hard", 30),
            ]
            
            logger.info(f"[INIT] {len(self.agent_dependencies)} agent dependencies configured")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize dependencies: {e}")
    
    async def _subscribe_to_events(self):
        """Subscribe to relevant events"""
        try:
            # Subscribe to agent heartbeats
            await self.event_bus.subscribe(
                EventTypes.AGENT_HEARTBEAT,
                self._handle_agent_heartbeat
            )
            
            # Subscribe to agent errors
            await self.event_bus.subscribe(
                EventTypes.AGENT_ERROR,
                self._handle_agent_error
            )
            
            # Subscribe to system events
            await self.event_bus.subscribe(
                EventTypes.SYSTEM_ERROR,
                self._handle_system_error
            )
            
            logger.info("[EVENTS] Subscribed to health monitoring events")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to events: {e}")
    
    async def _health_monitoring_loop(self):
        """Main health monitoring loop"""
        while self.is_running:
            try:
                await self._check_all_agents_health()
                await self._update_agent_statuses()
                await self._check_dependencies()
                await self._generate_health_alerts()
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Health monitoring loop failed: {e}")
                await asyncio.sleep(5)
    
    async def _performance_monitoring_loop(self):
        """Monitor agent performance metrics"""
        while self.is_running:
            try:
                await self._collect_performance_metrics()
                await self._analyze_performance_trends()
                await self._update_performance_scores()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"[ERROR] Performance monitoring failed: {e}")
                await asyncio.sleep(5)
    
    async def _system_monitoring_loop(self):
        """Monitor overall system health"""
        while self.is_running:
            try:
                await self._collect_system_metrics()
                await self._check_system_resources()
                await self._monitor_network_health()
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"[ERROR] System monitoring failed: {e}")
                await asyncio.sleep(5)
    
    async def _recovery_monitoring_loop(self):
        """Monitor and execute recovery actions"""
        while self.is_running:
            try:
                await self._check_recovery_conditions()
                await self._execute_recovery_actions()
                await self._cleanup_completed_recoveries()
                
                await asyncio.sleep(15)  # Check every 15 seconds
                
            except Exception as e:
                logger.error(f"[ERROR] Recovery monitoring failed: {e}")
                await asyncio.sleep(5)
    
    async def _handle_agent_heartbeat(self, event_data: Dict[str, Any]):
        """Handle agent heartbeat events"""
        try:
            agent_name = event_data.get('agent_name')
            if not agent_name:
                return
            
            # Update agent health
            if agent_name not in self.agent_health:
                self.agent_health[agent_name] = AgentHealth(
                    agent_name=agent_name,
                    status=AgentStatus.HEALTHY,
                    last_heartbeat=datetime.now(),
                    last_activity=datetime.now()
                )
            else:
                health = self.agent_health[agent_name]
                health.last_heartbeat = datetime.now()
                health.last_activity = datetime.now()
                
                # Update status if agent was previously failed
                if health.status in [AgentStatus.FAILED, AgentStatus.CRITICAL]:
                    health.status = AgentStatus.HEALTHY
                    logger.info(f"[RECOVERY] Agent {agent_name} recovered")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle heartbeat for {agent_name}: {e}")
    
    async def _handle_agent_error(self, event_data: Dict[str, Any]):
        """Handle agent error events"""
        try:
            agent_name = event_data.get('agent_name')
            error_type = event_data.get('error_type', 'unknown')
            error_message = event_data.get('error_message', '')
            
            if agent_name in self.agent_health:
                health = self.agent_health[agent_name]
                health.error_count += 1
                
                # Update status based on error count
                if health.error_count >= 10:
                    health.status = AgentStatus.FAILED
                    self.failed_agents.add(agent_name)
                elif health.error_count >= 5:
                    health.status = AgentStatus.CRITICAL
                else:
                    health.status = AgentStatus.WARNING
                
                logger.warning(f"[ERROR] Agent {agent_name} error #{health.error_count}: {error_message}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle agent error: {e}")
    
    async def _check_all_agents_health(self):
        """Check health of all registered agents"""
        try:
            current_time = datetime.now()
            
            for agent_name, health in self.agent_health.items():
                # Check heartbeat timeout
                time_since_heartbeat = (current_time - health.last_heartbeat).total_seconds()
                
                if time_since_heartbeat > self.heartbeat_timeout:
                    if health.status != AgentStatus.FAILED:
                        health.status = AgentStatus.FAILED
                        self.failed_agents.add(agent_name)
                        logger.error(f"[TIMEOUT] Agent {agent_name} heartbeat timeout: {time_since_heartbeat:.1f}s")
                
                # Update uptime
                health.uptime = (current_time - health.last_activity).total_seconds()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to check agent health: {e}")
    
    async def restart_agent(self, agent_name: str) -> bool:
        """Restart a failed agent"""
        try:
            if agent_name in self.restarting_agents:
                logger.warning(f"[RESTART] Agent {agent_name} already restarting")
                return False
            
            health = self.agent_health.get(agent_name)
            if not health:
                logger.error(f"[RESTART] Unknown agent: {agent_name}")
                return False
            
            if health.restart_count >= self.max_restart_attempts:
                logger.error(f"[RESTART] Max restart attempts reached for {agent_name}")
                return False
            
            self.restarting_agents.add(agent_name)
            health.status = AgentStatus.RESTARTING
            health.restart_count += 1
            
            # Send restart command
            await self.event_bus.publish(
                EventTypes.AGENT_RESTART_REQUEST,
                {
                    'agent_name': agent_name,
                    'restart_count': health.restart_count,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            logger.info(f"[RESTART] Initiated restart for agent {agent_name} (attempt #{health.restart_count})")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to restart agent {agent_name}: {e}")
            return False
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive system health summary"""
        try:
            healthy_agents = sum(1 for h in self.agent_health.values() if h.status == AgentStatus.HEALTHY)
            total_agents = len(self.agent_health)
            
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_health': 'healthy' if len(self.failed_agents) == 0 else 'degraded',
                'total_agents': total_agents,
                'healthy_agents': healthy_agents,
                'failed_agents': len(self.failed_agents),
                'restarting_agents': len(self.restarting_agents),
                'system_uptime': (datetime.now() - self.start_time).total_seconds() if hasattr(self, 'start_time') else 0,
                'agent_details': {
                    name: {
                        'status': health.status.value,
                        'error_count': health.error_count,
                        'restart_count': health.restart_count,
                        'uptime': health.uptime,
                        'performance_score': health.performance_score
                    }
                    for name, health in self.agent_health.items()
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate health summary: {e}")
            return {'error': str(e)}
