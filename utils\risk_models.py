#!/usr/bin/env python3
"""
Risk Management Data Models
Comprehensive data structures for risk management and trade validation

Features:
- Trade validation and risk assessment models
- Position and portfolio tracking structures
- Risk metrics and performance indicators
- Margin and capital allocation models
- Real-time monitoring data structures
"""

import os
import sys
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] ENUMS AND CONSTANTS
# ═══════════════════════════════════════════════════════════════════════════════

class TradeDirection(Enum):
    """Trade direction enumeration"""
    LONG = 1
    SHORT = -1
    EXIT = 0

class ProductType(Enum):
    """Product type enumeration"""
    MIS = "MIS"  # Margin Intraday Square-off
    CNC = "CNC"  # Cash and Carry
    NRML = "NRML"  # Normal

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    SL = "SL"
    SL_M = "SL-M"

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    ACKNOWLEDGED = "ACKNOWLEDGED"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"

class RiskLevel(Enum):
    """Risk level enumeration"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class ValidationStatus(Enum):
    """Validation status enumeration"""
    PASSED = "PASSED"
    FAILED = "FAILED"
    WARNING = "WARNING"
    PENDING = "PENDING"

# ═══════════════════════════════════════════════════════════════════════════════
# [TARGET] TRADE VALIDATION MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class TradeRequest:
    """Trade request for validation"""
    signal_id: str
    symbol: str
    exchange: str
    strategy_name: str
    direction: TradeDirection
    entry_price: float
    stop_loss: float
    take_profit: float
    quantity: int
    product_type: ProductType
    order_type: OrderType
    
    # Risk parameters
    risk_amount: float
    capital_allocated: float
    risk_reward_ratio: float
    
    # Market context
    market_regime: Optional[str] = None
    volatility_percentile: Optional[float] = None
    liquidity_score: Optional[float] = None
    
    # Timing
    timestamp: datetime = field(default_factory=datetime.now)
    expiry_time: Optional[datetime] = None
    
    # Metadata
    confidence: float = 0.0
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationResult:
    """Result of trade validation"""
    trade_request: TradeRequest
    is_valid: bool
    validation_status: ValidationStatus
    risk_level: RiskLevel
    
    # Validation details
    passed_checks: List[str] = field(default_factory=list)
    failed_checks: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Risk metrics
    margin_required: float = 0.0
    margin_available: float = 0.0
    margin_utilization_percent: float = 0.0
    position_size_percent: float = 0.0
    portfolio_risk_percent: float = 0.0
    
    # Rejection reason
    rejection_reason: Optional[str] = None
    rejection_code: Optional[str] = None
    
    # Validation timestamp
    validation_time: datetime = field(default_factory=datetime.now)
    
    # Additional metrics
    metrics: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RiskAssessment:
    """Risk assessment result for trade requests"""
    symbol: str
    signal_id: str
    risk_score: float
    position_size: int
    max_loss: float
    risk_reward_ratio: float
    approved: bool
    rejection_reason: str
    timestamp: datetime
    additional_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics"""
    # Portfolio level metrics
    total_capital: float
    available_capital: float
    utilized_capital: float
    capital_utilization_percent: float
    
    # Risk exposure
    total_risk_amount: float
    daily_risk_amount: float
    portfolio_risk_percent: float
    daily_risk_percent: float
    
    # Position metrics
    total_positions: int
    long_positions: int
    short_positions: int
    max_position_size: float
    avg_position_size: float
    
    # Performance metrics
    total_pnl: float
    realized_pnl: float
    unrealized_pnl: float
    daily_pnl: float
    max_drawdown: float
    current_drawdown: float
    
    # Margin metrics
    total_margin_required: float
    available_margin: float
    margin_utilization_percent: float
    
    # Risk ratios
    sharpe_ratio: Optional[float] = None
    sortino_ratio: Optional[float] = None
    calmar_ratio: Optional[float] = None
    
    # Timestamp
    timestamp: datetime = field(default_factory=datetime.now)

# ═══════════════════════════════════════════════════════════════════════════════
# [METRICS] POSITION AND PORTFOLIO MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class Position:
    """Individual position tracking"""
    position_id: str
    symbol: str
    exchange: str
    strategy_name: str
    
    # Position details
    direction: TradeDirection
    quantity: int
    entry_price: float
    current_price: float
    stop_loss: float
    take_profit: float
    
    # Financial metrics
    market_value: float
    pnl: float
    pnl_percent: float
    unrealized_pnl: float
    realized_pnl: float
    
    # Risk metrics
    risk_amount: float
    capital_allocated: float
    margin_required: float
    
    # Status and timing
    is_open: bool
    entry_time: datetime
    last_update_time: datetime
    exit_time: Optional[datetime] = None
    
    # Performance tracking
    max_profit: float = 0.0
    max_loss: float = 0.0
    holding_time_minutes: int = 0
    
    # Metadata
    product_type: ProductType = ProductType.MIS
    order_ids: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Portfolio:
    """Portfolio-level tracking"""
    portfolio_id: str
    name: str
    
    # Capital information
    total_capital: float
    available_capital: float
    utilized_capital: float
    reserved_capital: float
    
    # Positions
    positions: List[Position] = field(default_factory=list)
    closed_positions: List[Position] = field(default_factory=list)
    
    # Performance metrics
    total_pnl: float = field(default=0.0)
    daily_pnl: float = field(default=0.0)
    realized_pnl: float = field(default=0.0)
    unrealized_pnl: float = field(default=0.0)
    
    # Risk metrics
    total_risk_exposure: float = field(default=0.0)
    max_drawdown: float = field(default=0.0)
    current_drawdown: float = field(default=0.0)
    
    # Statistics
    total_trades: int = field(default=0)
    winning_trades: int = field(default=0)
    losing_trades: int = field(default=0)
    win_rate: float = field(default=0.0)
    avg_win: float = field(default=0.0)
    avg_loss: float = field(default=0.0)
    profit_factor: float = field(default=0.0)
    
    # Timestamps
    created_time: datetime = field(default_factory=datetime.now)
    last_update_time: datetime = field(default_factory=datetime.now)

# ═══════════════════════════════════════════════════════════════════════════════
# 🚨 RISK MONITORING MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class RiskAlert:
    """Risk alert notification"""
    alert_id: str
    alert_type: str
    severity: RiskLevel
    title: str
    message: str
    
    # Context
    symbol: Optional[str] = None
    strategy: Optional[str] = None
    position_id: Optional[str] = None
    
    # Metrics
    current_value: Optional[float] = None
    threshold_value: Optional[float] = None
    breach_percent: Optional[float] = None
    
    # Status
    is_active: bool = True
    acknowledged: bool = False
    resolved: bool = False
    
    # Timestamps
    created_time: datetime = field(default_factory=datetime.now)
    acknowledged_time: Optional[datetime] = None
    resolved_time: Optional[datetime] = None
    
    # Actions taken
    actions_taken: List[str] = field(default_factory=list)
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class DrawdownEvent:
    """Drawdown tracking event"""
    event_id: str
    start_time: datetime
    end_time: Optional[datetime]
    
    # Drawdown metrics
    peak_value: float
    trough_value: float
    drawdown_amount: float
    drawdown_percent: float
    
    # Duration
    duration_minutes: int = 0
    is_ongoing: bool = True
    
    # Recovery
    recovery_time: Optional[datetime] = None
    recovery_duration_minutes: Optional[int] = None
    
    # Context
    trigger_event: Optional[str] = None
    positions_affected: List[str] = field(default_factory=list)
    
    # Actions
    risk_actions_taken: List[str] = field(default_factory=list)

@dataclass
class PerformanceSnapshot:
    """Performance snapshot for monitoring"""
    snapshot_id: str
    timestamp: datetime
    
    # Capital metrics
    total_capital: float
    available_capital: float
    utilized_capital: float
    
    # PnL metrics
    total_pnl: float
    daily_pnl: float
    unrealized_pnl: float
    realized_pnl: float
    
    # Position metrics
    total_positions: int
    long_positions: int
    short_positions: int
    
    # Risk metrics
    total_risk_exposure: float
    margin_utilization: float
    current_drawdown: float
    
    # Performance ratios
    win_rate: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: Optional[float] = None
    
    # Market context
    market_regime: Optional[str] = None
    volatility_level: Optional[str] = None
    
    # System metrics
    active_strategies: int = 0
    api_latency_ms: float = 0.0
    system_load_percent: float = 0.0

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def calculate_risk_reward_ratio(entry_price: float, stop_loss: float, take_profit: float, 
                               direction: TradeDirection) -> float:
    """Calculate risk-reward ratio"""
    try:
        if direction == TradeDirection.LONG:
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
        else:  # SHORT
            risk = abs(stop_loss - entry_price)
            reward = abs(entry_price - take_profit)
        
        return reward / risk if risk > 0 else 0.0
    except:
        return 0.0

def calculate_position_size_percent(position_value: float, total_capital: float) -> float:
    """Calculate position size as percentage of capital"""
    try:
        return (position_value / total_capital) * 100 if total_capital > 0 else 0.0
    except:
        return 0.0

def calculate_win_rate(winning_trades: int, total_trades: int) -> float:
    """Calculate win rate percentage"""
    try:
        return (winning_trades / total_trades) * 100 if total_trades > 0 else 0.0
    except:
        return 0.0

def calculate_profit_factor(gross_profit: float, gross_loss: float) -> float:
    """Calculate profit factor"""
    try:
        return gross_profit / abs(gross_loss) if gross_loss != 0 else 0.0
    except:
        return 0.0

def format_risk_level(risk_level: RiskLevel) -> str:
    """Format risk level for display"""
    colors = {
        RiskLevel.LOW: "[LOW]",
        RiskLevel.MEDIUM: "[MED]",
        RiskLevel.HIGH: "[HIGH]",
        RiskLevel.CRITICAL: "[CRIT]"
    }
    return f"{colors.get(risk_level, '[UNK]')} {risk_level.value}"

def create_position_id(symbol: str, strategy: str, timestamp: datetime) -> str:
    """Create unique position ID"""
    return f"{symbol}_{strategy}_{timestamp.strftime('%Y%m%d_%H%M%S')}"

def create_alert_id(alert_type: str, timestamp: datetime) -> str:
    """Create unique alert ID"""
    return f"{alert_type}_{timestamp.strftime('%Y%m%d_%H%M%S_%f')}"
