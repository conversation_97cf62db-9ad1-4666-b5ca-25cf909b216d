#!/usr/bin/env python3
"""
Circuit Breaker and Emergency Stop System
=========================================

Comprehensive circuit breaker system for production trading with multiple
layers of protection and automatic recovery mechanisms.

Features:
- Multiple circuit breaker types (loss, drawdown, volatility, API failures)
- Emergency stop mechanisms for market crashes and system failures
- Automatic recovery and gradual re-engagement
- Real-time monitoring and alerting
- Configurable thresholds and actions
- Kill switch functionality
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class CircuitBreakerType(Enum):
    """Types of circuit breakers"""
    DAILY_LOSS = "daily_loss"
    DRAWDOWN = "drawdown"
    LEVERAGE = "leverage"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    API_FAILURES = "api_failures"
    VOLATILITY_SPIKE = "volatility_spike"
    POSITION_CONCENTRATION = "position_concentration"
    SYSTEM_OVERLOAD = "system_overload"

class EmergencyStopType(Enum):
    """Types of emergency stops"""
    MARKET_CRASH = "market_crash"
    FLASH_CRASH = "flash_crash"
    DATA_FEED_FAILURE = "data_feed_failure"
    NETWORK_FAILURE = "network_failure"
    SYSTEM_CRITICAL = "system_critical"
    MANUAL_KILL_SWITCH = "manual_kill_switch"

class BreakerState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Breaker triggered
    HALF_OPEN = "half_open"  # Testing recovery

class ActionType(Enum):
    """Actions to take when breaker triggers"""
    STOP_NEW_TRADES = "stop_new_trades"
    STOP_ALL_TRADING = "stop_all_trading"
    REDUCE_POSITION_SIZES = "reduce_position_sizes"
    EMERGENCY_EXIT_ALL = "emergency_exit_all"
    PAUSE_STRATEGY = "pause_strategy"
    SWITCH_TO_BACKUP = "switch_to_backup"
    SEND_ALERT = "send_alert"

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    name: str
    breaker_type: CircuitBreakerType
    threshold: float
    window_seconds: int = 300  # 5 minutes
    cooldown_seconds: int = 1800  # 30 minutes
    action: ActionType = ActionType.STOP_NEW_TRADES
    enabled: bool = True
    auto_recovery: bool = True
    recovery_threshold: Optional[float] = None

@dataclass
class CircuitBreakerState:
    """Circuit breaker runtime state"""
    config: CircuitBreakerConfig
    state: BreakerState = BreakerState.CLOSED
    current_value: float = 0.0
    trigger_time: Optional[datetime] = None
    recovery_time: Optional[datetime] = None
    trigger_count: int = 0
    last_check: datetime = field(default_factory=datetime.now)
    
@dataclass
class EmergencyStopConfig:
    """Emergency stop configuration"""
    name: str
    stop_type: EmergencyStopType
    condition_check: str  # Function name or condition
    threshold: float
    action: ActionType
    enabled: bool = True
    auto_recovery: bool = False
    recovery_delay_seconds: int = 3600  # 1 hour

@dataclass
class EmergencyStopState:
    """Emergency stop runtime state"""
    config: EmergencyStopConfig
    triggered: bool = False
    trigger_time: Optional[datetime] = None
    recovery_time: Optional[datetime] = None
    trigger_count: int = 0

class CircuitBreakerSystem:
    """
    Comprehensive circuit breaker and emergency stop system
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Circuit breakers
        self.circuit_breakers: Dict[str, CircuitBreakerState] = {}
        self.emergency_stops: Dict[str, EmergencyStopState] = {}
        
        # System state
        self.system_enabled = True
        self.trading_enabled = True
        self.emergency_mode = False
        
        # Monitoring
        self.monitoring_active = True
        self.check_interval = 10  # seconds
        
        # Action handlers
        self.action_handlers: Dict[ActionType, Callable] = {}
        
        # Metrics
        self.metrics = {
            'total_triggers': 0,
            'emergency_stops': 0,
            'auto_recoveries': 0,
            'manual_recoveries': 0
        }
        
        # Initialize default circuit breakers
        self._initialize_default_breakers()
        
        logger.info("[INIT] Circuit breaker system initialized")
    
    def _initialize_default_breakers(self):
        """Initialize default circuit breakers"""
        try:
            # Daily loss circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="Daily Loss Limit",
                breaker_type=CircuitBreakerType.DAILY_LOSS,
                threshold=0.03,  # 3% daily loss
                window_seconds=86400,  # 24 hours
                cooldown_seconds=3600,  # 1 hour
                action=ActionType.STOP_NEW_TRADES,
                recovery_threshold=0.02  # Recover when loss < 2%
            ))
            
            # Maximum drawdown circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="Maximum Drawdown",
                breaker_type=CircuitBreakerType.DRAWDOWN,
                threshold=0.10,  # 10% drawdown
                cooldown_seconds=7200,  # 2 hours
                action=ActionType.STOP_ALL_TRADING,
                recovery_threshold=0.08  # Recover when drawdown < 8%
            ))
            
            # Leverage circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="Leverage Limit",
                breaker_type=CircuitBreakerType.LEVERAGE,
                threshold=2.0,  # 2x leverage
                cooldown_seconds=1800,  # 30 minutes
                action=ActionType.REDUCE_POSITION_SIZES,
                recovery_threshold=1.5  # Recover when leverage < 1.5x
            ))
            
            # Consecutive losses circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="Consecutive Losses",
                breaker_type=CircuitBreakerType.CONSECUTIVE_LOSSES,
                threshold=5,  # 5 consecutive losses
                cooldown_seconds=3600,  # 1 hour
                action=ActionType.PAUSE_STRATEGY,
                recovery_threshold=0  # Manual recovery only
            ))
            
            # API failure circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="API Failure Rate",
                breaker_type=CircuitBreakerType.API_FAILURES,
                threshold=0.5,  # 50% failure rate
                window_seconds=300,  # 5 minutes
                cooldown_seconds=900,  # 15 minutes
                action=ActionType.SWITCH_TO_BACKUP,
                recovery_threshold=0.1  # Recover when failure rate < 10%
            ))
            
            # Volatility spike circuit breaker
            self.add_circuit_breaker(CircuitBreakerConfig(
                name="Volatility Spike",
                breaker_type=CircuitBreakerType.VOLATILITY_SPIKE,
                threshold=3.0,  # 3x normal volatility
                window_seconds=300,  # 5 minutes
                cooldown_seconds=1800,  # 30 minutes
                action=ActionType.REDUCE_POSITION_SIZES,
                recovery_threshold=1.5  # Recover when volatility < 1.5x normal
            ))
            
            # Initialize emergency stops
            self._initialize_emergency_stops()
            
            logger.info(f"[INIT] Initialized {len(self.circuit_breakers)} circuit breakers")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize default breakers: {e}")
    
    def _initialize_emergency_stops(self):
        """Initialize emergency stop mechanisms"""
        try:
            # Market crash emergency stop
            self.add_emergency_stop(EmergencyStopConfig(
                name="Market Crash Detection",
                stop_type=EmergencyStopType.MARKET_CRASH,
                condition_check="check_market_crash",
                threshold=0.05,  # 5% market drop
                action=ActionType.EMERGENCY_EXIT_ALL,
                auto_recovery=False
            ))
            
            # Flash crash emergency stop
            self.add_emergency_stop(EmergencyStopConfig(
                name="Flash Crash Detection",
                stop_type=EmergencyStopType.FLASH_CRASH,
                condition_check="check_flash_crash",
                threshold=0.03,  # 3% drop in 1 minute
                action=ActionType.EMERGENCY_EXIT_ALL,
                auto_recovery=False
            ))
            
            # Data feed failure emergency stop
            self.add_emergency_stop(EmergencyStopConfig(
                name="Data Feed Failure",
                stop_type=EmergencyStopType.DATA_FEED_FAILURE,
                condition_check="check_data_staleness",
                threshold=300,  # 5 minutes stale data
                action=ActionType.STOP_ALL_TRADING,
                auto_recovery=True,
                recovery_delay_seconds=600  # 10 minutes
            ))
            
            # System critical emergency stop
            self.add_emergency_stop(EmergencyStopConfig(
                name="System Critical",
                stop_type=EmergencyStopType.SYSTEM_CRITICAL,
                condition_check="check_system_health",
                threshold=0.95,  # 95% resource usage
                action=ActionType.STOP_NEW_TRADES,
                auto_recovery=True,
                recovery_delay_seconds=300  # 5 minutes
            ))
            
            logger.info(f"[INIT] Initialized {len(self.emergency_stops)} emergency stops")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize emergency stops: {e}")
    
    def add_circuit_breaker(self, config: CircuitBreakerConfig):
        """Add a new circuit breaker"""
        try:
            state = CircuitBreakerState(config=config)
            self.circuit_breakers[config.name] = state
            logger.info(f"[BREAKER] Added circuit breaker: {config.name}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add circuit breaker: {e}")
    
    def add_emergency_stop(self, config: EmergencyStopConfig):
        """Add a new emergency stop"""
        try:
            state = EmergencyStopState(config=config)
            self.emergency_stops[config.name] = state
            logger.info(f"[EMERGENCY] Added emergency stop: {config.name}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add emergency stop: {e}")
    
    def register_action_handler(self, action_type: ActionType, handler: Callable):
        """Register an action handler"""
        self.action_handlers[action_type] = handler
        logger.info(f"[HANDLER] Registered action handler for {action_type.value}")
    
    async def start_monitoring(self):
        """Start the monitoring loop"""
        try:
            self.monitoring_active = True
            
            # Start monitoring tasks
            asyncio.create_task(self._circuit_breaker_monitoring_loop())
            asyncio.create_task(self._emergency_stop_monitoring_loop())
            asyncio.create_task(self._recovery_monitoring_loop())
            
            logger.info("[START] Circuit breaker monitoring started")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.monitoring_active = False
        logger.info("[STOP] Circuit breaker monitoring stopped")
    
    async def _circuit_breaker_monitoring_loop(self):
        """Monitor circuit breakers"""
        while self.monitoring_active:
            try:
                for name, breaker in self.circuit_breakers.items():
                    if breaker.config.enabled:
                        await self._check_circuit_breaker(name, breaker)
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Circuit breaker monitoring failed: {e}")
                await asyncio.sleep(5)
    
    async def _emergency_stop_monitoring_loop(self):
        """Monitor emergency stop conditions"""
        while self.monitoring_active:
            try:
                for name, stop in self.emergency_stops.items():
                    if stop.config.enabled and not stop.triggered:
                        await self._check_emergency_stop(name, stop)
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"[ERROR] Emergency stop monitoring failed: {e}")
                await asyncio.sleep(5)
    
    async def _recovery_monitoring_loop(self):
        """Monitor recovery conditions"""
        while self.monitoring_active:
            try:
                # Check circuit breaker recovery
                for name, breaker in self.circuit_breakers.items():
                    if breaker.state == BreakerState.OPEN and breaker.config.auto_recovery:
                        await self._check_breaker_recovery(name, breaker)
                
                # Check emergency stop recovery
                for name, stop in self.emergency_stops.items():
                    if stop.triggered and stop.config.auto_recovery:
                        await self._check_emergency_recovery(name, stop)
                
                await asyncio.sleep(30)  # Check recovery every 30 seconds
                
            except Exception as e:
                logger.error(f"[ERROR] Recovery monitoring failed: {e}")
                await asyncio.sleep(10)
    
    async def _check_circuit_breaker(self, name: str, breaker: CircuitBreakerState):
        """Check if circuit breaker should trigger"""
        try:
            # Get current value based on breaker type
            current_value = await self._get_breaker_value(breaker.config.breaker_type)
            breaker.current_value = current_value
            breaker.last_check = datetime.now()
            
            # Check if breaker should trigger
            if breaker.state == BreakerState.CLOSED and current_value >= breaker.config.threshold:
                await self._trigger_circuit_breaker(name, breaker)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to check circuit breaker {name}: {e}")
    
    async def _trigger_circuit_breaker(self, name: str, breaker: CircuitBreakerState):
        """Trigger a circuit breaker"""
        try:
            breaker.state = BreakerState.OPEN
            breaker.trigger_time = datetime.now()
            breaker.trigger_count += 1
            
            self.metrics['total_triggers'] += 1
            
            logger.critical(f"[CIRCUIT BREAKER] {name} TRIGGERED - "
                          f"Value: {breaker.current_value:.3f}, "
                          f"Threshold: {breaker.config.threshold:.3f}")
            
            # Execute action
            await self._execute_action(breaker.config.action, {
                'breaker_name': name,
                'breaker_type': breaker.config.breaker_type.value,
                'current_value': breaker.current_value,
                'threshold': breaker.config.threshold
            })
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger circuit breaker {name}: {e}")
    
    async def _execute_action(self, action: ActionType, context: Dict[str, Any]):
        """Execute a circuit breaker action"""
        try:
            logger.info(f"[ACTION] Executing action: {action.value}")
            
            # Execute registered handler if available
            if action in self.action_handlers:
                await self.action_handlers[action](context)
            else:
                # Default action implementations
                if action == ActionType.STOP_NEW_TRADES:
                    self.trading_enabled = False
                elif action == ActionType.STOP_ALL_TRADING:
                    self.trading_enabled = False
                    self.system_enabled = False
                elif action == ActionType.EMERGENCY_EXIT_ALL:
                    self.emergency_mode = True
                    self.trading_enabled = False
                    self.system_enabled = False
                
                logger.warning(f"[ACTION] No handler registered for {action.value}, using default")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to execute action {action.value}: {e}")
    
    async def manual_kill_switch(self, reason: str = "Manual intervention"):
        """Manual kill switch to stop all trading immediately"""
        try:
            logger.critical(f"[KILL SWITCH] Manual kill switch activated: {reason}")
            
            # Trigger manual emergency stop
            manual_stop = EmergencyStopState(
                config=EmergencyStopConfig(
                    name="Manual Kill Switch",
                    stop_type=EmergencyStopType.MANUAL_KILL_SWITCH,
                    condition_check="manual",
                    threshold=0,
                    action=ActionType.EMERGENCY_EXIT_ALL,
                    auto_recovery=False
                )
            )
            
            manual_stop.triggered = True
            manual_stop.trigger_time = datetime.now()
            manual_stop.trigger_count = 1
            
            self.emergency_stops["Manual Kill Switch"] = manual_stop
            self.metrics['emergency_stops'] += 1
            
            # Execute emergency action
            await self._execute_action(ActionType.EMERGENCY_EXIT_ALL, {
                'reason': reason,
                'manual': True
            })
            
            self.emergency_mode = True
            self.trading_enabled = False
            self.system_enabled = False
            
        except Exception as e:
            logger.error(f"[ERROR] Manual kill switch failed: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            active_breakers = [
                name for name, breaker in self.circuit_breakers.items()
                if breaker.state == BreakerState.OPEN
            ]
            
            active_emergency_stops = [
                name for name, stop in self.emergency_stops.items()
                if stop.triggered
            ]
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system_enabled': self.system_enabled,
                'trading_enabled': self.trading_enabled,
                'emergency_mode': self.emergency_mode,
                'monitoring_active': self.monitoring_active,
                'active_circuit_breakers': active_breakers,
                'active_emergency_stops': active_emergency_stops,
                'circuit_breaker_count': len(self.circuit_breakers),
                'emergency_stop_count': len(self.emergency_stops),
                'metrics': self.metrics,
                'circuit_breakers': {
                    name: {
                        'state': breaker.state.value,
                        'current_value': breaker.current_value,
                        'threshold': breaker.config.threshold,
                        'trigger_count': breaker.trigger_count,
                        'last_check': breaker.last_check.isoformat()
                    }
                    for name, breaker in self.circuit_breakers.items()
                }
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get system status: {e}")
            return {'error': str(e)}
