data_management:
  historical_days: 25
  data_directory: "data/live"
  file_format: "parquet"
  compression: "brotli"
  batch_size: 15
  max_concurrent_requests: 3
  api_rate_limit:
    requests_per_minute: 85
    requests_per_second: 3
  stock_universe:
    max_stocks_to_process: 225
    exclude_symbols: []
    include_only: []
  data_quality:
    min_data_points: 1000
    max_missing_percentage: 5
    outlier_detection: true
    outlier_method: "iqr"
    outlier_threshold: 1.5

smartapi:
  api_key: "${SMARTAPI_API_KEY}"
  username: "${SMARTAPI_USERNAME}"
  password: "${SMARTAPI_PASSWORD}"
  totp_token: "${SMARTAPI_TOTP_TOKEN}"
