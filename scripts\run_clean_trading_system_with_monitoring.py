#!/usr/bin/env python3
"""
CLEAN TRADING SYSTEM RUNNER WITH ENHANCED MONITORING
Enhanced version with better logging and monitoring to track system status.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
import sys
import time

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Import the original system
from scripts.run_clean_trading_system import CleanTradingSystem

logger = logging.getLogger(__name__)

class MonitoredCleanTradingSystem(CleanTradingSystem):
    """Enhanced trading system with better monitoring and logging."""
    
    def __init__(self, mode: str, skip_download: bool = False):
        super().__init__(mode, skip_download)
        self.start_time = None
        self.last_status_update = None
        self.status_update_interval = 200  # Status update every 200 seconds (20 signal cycles)
        self.last_signal_cycle_count = 0  # Track signal cycles for status updates
        
    async def start(self):
        """Start the trading system with enhanced monitoring."""
        self.start_time = datetime.now()
        logger.info(f"🚀 Starting Clean Trading System at {self.start_time}")
        
        # Start the original system
        await super().start()
        
    async def _check_agent_health(self):
        """Enhanced health check with detailed logging."""
        try:
            current_time = datetime.now()

            # Check if we should update status based on signal cycles
            should_update_status = False

            # Get current signal cycle count from signal agent
            if 'signal_generation' in self.agents:
                signal_agent = self.agents['signal_generation']
                if hasattr(signal_agent, 'loop_count'):
                    current_cycle_count = signal_agent.loop_count

                    # Update status every 20 signal cycles
                    if current_cycle_count - self.last_signal_cycle_count >= 20:
                        should_update_status = True
                        self.last_signal_cycle_count = current_cycle_count

            # Fallback to time-based updates if signal cycle tracking fails
            if not should_update_status and (self.last_status_update is None or
                (current_time - self.last_status_update).seconds >= self.status_update_interval):
                should_update_status = True

            if should_update_status:
                await self._print_detailed_status()
                self.last_status_update = current_time

            # Original health check
            await super()._check_agent_health()

        except Exception as e:
            logger.error(f"Error in enhanced health check: {e}")
    
    async def _print_detailed_status(self):
        """Print detailed system status."""
        try:
            current_time = datetime.now()
            uptime = current_time - self.start_time if self.start_time else timedelta(0)
            
            logger.info("=" * 80)
            logger.info(f"📊 SYSTEM STATUS UPDATE - {current_time.strftime('%H:%M:%S')}")
            logger.info(f"⏱️  System Uptime: {uptime}")
            logger.info(f"🎯 Trading Mode: {self.mode}")
            logger.info(f"📈 Selected Stocks: {len(getattr(self.config, 'selected_stocks', []))}")
            
            # Agent status
            logger.info("🤖 Agent Status:")
            for agent_name, agent in self.agents.items():
                status = "🟢 Running" if agent.running else "🔴 Stopped"
                logger.info(f"   {agent_name}: {status}")
            
            # Market data status
            if 'market_data' in self.agents:
                market_agent = self.agents['market_data']
                try:
                    if hasattr(market_agent, 'live_candle_aggregators'):
                        active_symbols = len(market_agent.live_candle_aggregators)
                    else:
                        active_symbols = 0
                    ws_stats = getattr(market_agent, 'get_streaming_stats', lambda: {})()
                    logger.info(f"📡 Live Data: {active_symbols} symbols streaming | WS: {ws_stats}")
                except Exception:
                    logger.info("📡 Live Data: status unavailable")

            # Signal generation status
            if 'signal_generation' in self.agents:
                signal_agent = self.agents['signal_generation']
                try:
                    recent_signals = len([s for s in getattr(signal_agent, 'signal_history', [])
                                        if (current_time - s.timestamp).seconds < 300])
                    status = getattr(signal_agent, 'get_status', lambda: {})()
                    logger.info(f"🎯 Recent Signals (5min): {recent_signals} | {status}")
                except Exception:
                    logger.info("🎯 Recent Signals: status unavailable")

            # Execution agent stats
            if 'execution' in self.agents:
                exec_agent = self.agents['execution']
                try:
                    if hasattr(exec_agent, 'get_execution_statistics'):
                        stats = exec_agent.get_execution_statistics()
                    elif hasattr(exec_agent, 'get_execution_stats'):
                        stats = await exec_agent.get_execution_stats()
                    else:
                        stats = {}
                    logger.info(f"💹 Execution: {stats}")
                    
                    # Add virtual account balance logging for paper trading
                    if hasattr(exec_agent, 'paper_account') and exec_agent.paper_account:
                        account_summary = exec_agent.paper_account.get_account_summary()
                        max_trades = account_summary.get('today_trades', 0) + account_summary.get('trades_remaining_today', 0)
                        logger.info(f"💰 Virtual Account Balance: Rs.{account_summary.get('current_balance', 0):,.2f}")
                        logger.info(f"   Available Margin: Rs.{account_summary.get('available_margin', 0):,.2f}")
                        logger.info(f"   Used Margin: Rs.{account_summary.get('used_margin', 0):,.2f}")
                        logger.info(f"   Total PnL: Rs.{account_summary.get('total_pnl', 0):,.2f}")
                        logger.info(f"   Today Completed Trades: {account_summary.get('today_trades', 0)}/{max_trades}")
                        logger.info(f"   Active Positions: {account_summary.get('active_positions', 0)}")
                        logger.info(f"   Return %: {account_summary.get('return_percent', 0):.2f}%")
                        
                except Exception as e:
                    logger.info(f"💹 Execution: stats unavailable - {e}")

            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"Error printing detailed status: {e}")

async def main():
    parser = argparse.ArgumentParser(description='Clean Trading System with Enhanced Monitoring')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    parser.add_argument('--skip-download', action='store_true', help='Skip ML workflow entirely and use basic stock list')
    args = parser.parse_args()

    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # Enhanced logging format
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / 'clean_trading_system_monitored.log')
        ]
    )

    # Create monitored trading system
    trading_system = MonitoredCleanTradingSystem(args.mode, args.skip_download)
    trading_system._setup_signal_handlers()

    try:
        logger.info("🎬 Starting Clean Trading System with Enhanced Monitoring...")
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("[INTERRUPT] KeyboardInterrupt in main(), initiating shutdown...")
        if trading_system.running:
            await trading_system.stop()
    except Exception as e:
        logger.error(f"[FATAL] System error: {e}", exc_info=True)
        if trading_system.running:
            await trading_system.stop()
    finally:
        if trading_system.running:
            await trading_system.stop()
        logger.info("🏁 System shutdown complete.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] System shutdown complete.")
    except Exception as e:
        logger.error(f"[FATAL] Unhandled exception: {e}", exc_info=True)