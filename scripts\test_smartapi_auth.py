#!/usr/bin/env python3
"""
Test SmartAPI Authentication
Quick test to verify SmartAPI credentials and authentication.
"""

import asyncio
import logging
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
load_dotenv(Path(__file__).parent.parent / '.env')

from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_authentication():
    """Test SmartAPI authentication."""
    try:
        # Load credentials
        api_key = os.getenv('SMARTAPI_API_KEY')
        username = os.getenv('SMARTAPI_USERNAME')
        password = os.getenv('SMARTAPI_PASSWORD')
        totp_token = os.getenv('SMARTAPI_TOTP_TOKEN')
        
        logger.info("🔐 Testing SmartAPI Authentication...")
        logger.info(f"API Key: {'*' * (len(api_key) - 4) + api_key[-4:] if api_key else 'None'}")
        logger.info(f"Username: {'*' * (len(username) - 2) + username[-2:] if username else 'None'}")
        logger.info(f"Password: {'*' * 8 if password else 'None'}")
        logger.info(f"TOTP Token: {'*' * 8 if totp_token else 'None'}")
        
        if not all([api_key, username, password, totp_token]):
            logger.error("❌ Missing credentials!")
            return False
        
        # Create credentials
        credentials = SmartAPICredentials(api_key, username, password, totp_token)
        
        # Create client
        client = ModernSmartAPIClient(credentials)
        
        # Test authentication
        logger.info("🔄 Attempting authentication...")
        auth_success = await client.authenticate()
        
        if auth_success:
            logger.info("✅ Authentication successful!")
            logger.info(f"Auth Token: {'*' * 20}...{client.auth_token[-10:] if client.auth_token else 'None'}")
            logger.info(f"Feed Token: {'*' * 20}...{client.feed_token[-10:] if client.feed_token else 'None'}")
            return True
        else:
            logger.error("❌ Authentication failed!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Authentication test failed: {e}")
        return False

async def main():
    """Main test function."""
    logger.info("🧪 SmartAPI Authentication Test")
    logger.info("=" * 50)
    
    success = await test_authentication()
    
    logger.info("=" * 50)
    if success:
        logger.info("✅ Test completed successfully!")
    else:
        logger.info("❌ Test failed!")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test error: {e}")
        sys.exit(1)