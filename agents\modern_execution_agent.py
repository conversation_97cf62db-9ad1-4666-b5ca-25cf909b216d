#!/usr/bin/env python3
"""
MODERN EXECUTION AGENT
Modern execution agent for order management and trade execution

Features:
- Smart order routing and execution
- Paper trading and live trading support
- Order management and tracking
- Real-time position monitoring
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .base_agent import BaseAgent
from core.event_system import EventBus, Event, EventTypes
from utils.paper_trading import VirtualAccount, PaperTrade, OrderStatus, TransactionType

logger = logging.getLogger(__name__)

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LIMIT = "STOP_LIMIT"

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class TradingMode(Enum):
    PAPER = "paper"
    LIVE = "live"
    DEMO = "demo"

@dataclass
class Order:
    """Order data structure"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: float
    stop_price: Optional[float]
    status: OrderStatus
    created_at: datetime
    updated_at: datetime
    filled_quantity: int
    average_price: float
    metadata: Dict[str, Any]

class ModernExecutionAgent(BaseAgent):
    """
    Modern Execution Agent for order management and trade execution
    
    Responsibilities:
    - Execute approved trading signals
    - Manage orders and positions
    - Handle paper trading and live trading
    - Monitor order status and fills
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("ModernExecutionAgent", event_bus, config, session_id)
        
        # Trading mode
        try:
            self.trading_mode = TradingMode(config.mode)
        except ValueError:
            self.log_error(f"Invalid trading mode: {config.mode}. Defaulting to paper mode.")
            self.trading_mode = TradingMode.PAPER
        
        # Order management
        self.active_orders = {}
        self.completed_orders = {}
        self.positions = {}
        
        # Paper trading account
        self.paper_account = None
        if self.trading_mode == TradingMode.PAPER:
            self._initialize_paper_account()

        # SmartAPI client for live trading
        self.smartapi_client = None
        if self.trading_mode == TradingMode.LIVE:
            self._initialize_live_trading()

        # Background task handle
        self._order_task = None

        # Execution statistics
        self.execution_stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'total_volume': 0,
            'average_fill_time': 0
        }
    
    def _initialize_paper_account(self):
        """Initialize paper trading account"""
        try:
            paper_config = {
                'paper_trading': {
                    'initial_balance': self.config.initial_balance,
                    'commission_rate': 0.0003,
                    'flat_brokerage': 20.0,
                    'stt_rate': 0.001,
                    'gst_rate': 0.18,
                    'stamp_duty_rate': 0.00003
                }
            }
            
            self.paper_account = VirtualAccount(paper_config)
            self.log_info(f"Paper trading account initialized with balance: ₹{self.config.initial_balance:,.2f}")
            
        except Exception as e:
            self.log_error(f"Failed to initialize paper account: {e}")
    
    def _initialize_live_trading(self):
        """Initialize live trading connection"""
        try:
            # This would initialize SmartAPI client for live trading
            self.log_warning("Live trading mode - USE WITH EXTREME CAUTION!")
            # TODO: Initialize SmartAPI client
            
        except Exception as e:
            self.log_error(f"Failed to initialize live trading: {e}")
    
    async def initialize(self) -> bool:
        """Initialize the execution agent"""
        try:
            self.log_info("Initializing Modern Execution Agent...")
            
            # Subscribe to approved signals
            self.event_bus.subscribe(EventTypes.SIGNAL_RISK_APPROVED, self._handle_approved_signal)
            self.event_bus.subscribe(EventTypes.MARKET_DATA_UPDATE, self._handle_market_data_update)
            
            self.initialized = True
            self.log_info("Modern Execution Agent initialized successfully")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize Modern Execution Agent: {e}")
            return False
    
    async def start(self):
        """Start the execution agent"""
        try:
            # Ensure initialized (subscribe to events, etc.)
            if not self.initialized:
                init_ok = await self.initialize()
                if not init_ok:
                    self.log_error("Initialization failed during start()")
                    return False

            self.log_info("Starting Modern Execution Agent...")

            self.running = True

            # Start order monitoring loop in background
            self._order_task = asyncio.create_task(self._start_order_monitoring_loop())

            return True

        except Exception as e:
            self.log_error(f"Error starting Modern Execution Agent: {e}")
            return False

    async def _start_order_monitoring_loop(self):
        """Start the order monitoring loop"""
        try:
            self.log_info("Starting order monitoring loop...")
            
            while self.running:
                try:
                    # Monitor active orders
                    await self._monitor_active_orders()
                    
                    # Update positions
                    await self._update_positions()
                    
                    # Check for stop-loss triggers
                    await self._check_stop_loss_triggers()
                    
                    # Sleep for monitoring interval
                    await asyncio.sleep(5)  # Monitor every 5 seconds
                    
                except Exception as e:
                    self.log_error(f"Error in order monitoring loop: {e}")
                    await asyncio.sleep(2)
            
            self.log_info("Order monitoring loop ended")
            
        except Exception as e:
            self.log_error(f"Failed to start order monitoring loop: {e}")
    
    async def _handle_approved_signal(self, event: Event):
        """Handle approved trading signal"""
        try:
            signal = event.data.get('signal')
            risk_assessment = event.data.get('risk_assessment')
            
            if not signal or not risk_assessment:
                return
            
            # Create order from signal
            order = await self._create_order_from_signal(signal, risk_assessment)
            
            if order:
                # Increment orders_placed counter when order is created
                self.execution_stats['orders_placed'] += 1
                
                # Execute order
                success = await self._execute_order(order)
                
                if success:
                    self.log_info(f"Successfully executed order for {signal.symbol}")
                else:
                    self.log_error(f"Failed to execute order for {signal.symbol}")
            
            self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle approved signal: {e}")
    
    async def _create_order_from_signal(self, signal, risk_assessment) -> Optional[Order]:
        """Create order from trading signal"""
        try:
            order_id = f"{signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Determine order side
            side = OrderSide.BUY if signal.signal_type == "BUY" else OrderSide.SELL
            
            # Create order
            order = Order(
                order_id=order_id,
                symbol=signal.symbol,
                side=side,
                order_type=OrderType.MARKET,  # Use market orders for simplicity
                quantity=risk_assessment.position_size,
                price=signal.entry_price,
                stop_price=signal.stop_loss,
                status=OrderStatus.PENDING,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                filled_quantity=0,
                average_price=0.0,
                metadata={
                    'signal_id': getattr(signal, 'signal_id', 'unknown'),
                    'strategy': signal.strategy_name,
                    'target_price': signal.target_price,
                    'stop_loss': signal.stop_loss,
                    'risk_score': risk_assessment.risk_score
                }
            )
            
            return order
            
        except Exception as e:
            self.log_error(f"Failed to create order from signal: {e}")
            return None
    
    async def _execute_order(self, order: Order) -> bool:
        """Execute an order"""
        try:
            self.log_info(f"Executing {order.side.value} order for {order.symbol} - Qty: {order.quantity}")
            
            # Store order as active
            self.active_orders[order.order_id] = order
            
            if self.trading_mode.value == "paper":
                return await self._execute_paper_order(order)
            elif self.trading_mode.value == "live":
                return await self._execute_live_order(order)
            else:  # demo mode
                return await self._execute_demo_order(order)
            
        except Exception as e:
            self.log_error(f"Failed to execute order: {e}")
            return False
    
    async def _execute_paper_order(self, order: Order) -> bool:
        """Execute order in paper trading mode"""
        try:
            if not self.paper_account:
                self.log_error("Paper account not initialized")
                return False
            
            # Execute trade via virtual account
            tx_type = "BUY" if order.side == OrderSide.BUY else "SELL"
            success, message, paper_trade = await self.paper_account.execute_trade(
                symbol=order.symbol,
                exchange=getattr(self.config, 'exchange', 'NSE'),
                quantity=order.quantity,
                price=order.price,
                transaction_type=tx_type,
                order_type=order.order_type.value,
                product_type=getattr(self.config, 'product_type', 'MIS'),
                strategy_name=order.metadata.get('strategy', ''),
                signal_id=order.metadata.get('signal_id', '')
            )

            if success:
                # Update order status
                order.status = OrderStatus.EXECUTED
                order.filled_quantity = order.quantity
                order.average_price = order.price
                order.updated_at = datetime.now()
                
                # Move to completed orders
                self.completed_orders[order.order_id] = order
                if order.order_id in self.active_orders:
                    del self.active_orders[order.order_id]
                
                # Update statistics
                self.execution_stats['orders_filled'] += 1
                self.execution_stats['total_volume'] += order.quantity * order.price
                
                # Publish trade executed event
                await self._publish_trade_executed(order, paper_trade)
                
                self.log_info(f"Paper trade executed: {order.side.value} {order.quantity} {order.symbol} @ ₹{order.price}")
                return True
            else:
                order.status = OrderStatus.REJECTED
                order.updated_at = datetime.now()
                
                # Move rejected order to completed orders (not active)
                self.completed_orders[order.order_id] = order
                if order.order_id in self.active_orders:
                    del self.active_orders[order.order_id]
                
                self.log_error(f"Paper trade rejected for {order.symbol}: {message}")
                return False
            
        except Exception as e:
            self.log_error(f"Failed to execute paper order: {e}")
            order.status = OrderStatus.REJECTED
            order.updated_at = datetime.now()
            
            # Move rejected order to completed orders (not active)
            self.completed_orders[order.order_id] = order
            if order.order_id in self.active_orders:
                del self.active_orders[order.order_id]
            
            return False
    
    async def _execute_live_order(self, order: Order) -> bool:
        """Execute order in live trading mode"""
        try:
            # TODO: Implement live order execution using SmartAPI
            self.log_warning("Live order execution not implemented yet")
            
            # For now, simulate execution
            await asyncio.sleep(1)  # Simulate network delay
            
            order.status = OrderStatus.EXECUTED
            order.filled_quantity = order.quantity
            order.average_price = order.price
            order.updated_at = datetime.now()
            
            return True
            
        except Exception as e:
            self.log_error(f"Failed to execute live order: {e}")
            return False
    
    async def _execute_demo_order(self, order: Order) -> bool:
        """Execute order in demo mode"""
        try:
            # Simulate order execution
            await asyncio.sleep(0.5)  # Simulate execution delay
            
            # Simulate some slippage
            import random
            slippage = random.uniform(-0.001, 0.001)  # ±0.1% slippage
            execution_price = order.price * (1 + slippage)
            
            order.status = OrderStatus.EXECUTED
            order.filled_quantity = order.quantity
            order.average_price = execution_price
            order.updated_at = datetime.now()
            
            # Move to completed orders
            self.completed_orders[order.order_id] = order
            if order.order_id in self.active_orders:
                del self.active_orders[order.order_id]
            
            # Update statistics
            self.execution_stats['orders_filled'] += 1
            self.execution_stats['total_volume'] += order.quantity * execution_price
            
            # Publish trade executed event
            await self._publish_demo_trade_executed(order)
            
            self.log_info(f"Demo trade executed: {order.side.value} {order.quantity} {order.symbol} @ ₹{execution_price:.2f}")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to execute demo order: {e}")
            return False
    
    async def _publish_trade_executed(self, order: Order, paper_trade: PaperTrade):
        """Publish trade executed event"""
        try:
            await self.event_bus.publish(
                EventTypes.TRADE_EXECUTED,
                {
                    "order_id": order.order_id,
                    "symbol": order.symbol,
                    "side": order.side.value,
                    "quantity": order.filled_quantity,
                    "price": order.average_price,
                    "trade_value": order.filled_quantity * order.average_price,
                    "timestamp": order.updated_at,
                    "trading_mode": self.trading_mode.value,
                    "paper_trade": paper_trade,
                    "metadata": order.metadata
                },
                source=self.name
            )
            
        except Exception as e:
            self.log_error(f"Failed to publish trade executed event: {e}")
    
    async def _publish_demo_trade_executed(self, order: Order):
        """Publish demo trade executed event"""
        try:
            await self.event_bus.publish(
                EventTypes.TRADE_EXECUTED,
                {
                    "order_id": order.order_id,
                    "symbol": order.symbol,
                    "side": order.side.value,
                    "quantity": order.filled_quantity,
                    "price": order.average_price,
                    "trade_value": order.filled_quantity * order.average_price,
                    "timestamp": order.updated_at,
                    "trading_mode": self.trading_mode.value,
                    "metadata": order.metadata
                },
                source=self.name
            )
            
        except Exception as e:
            self.log_error(f"Failed to publish demo trade executed event: {e}")
    
    async def _monitor_active_orders(self):
        """Monitor active orders for status updates"""
        try:
            for order_id, order in list(self.active_orders.items()):
                # Check order age
                order_age = datetime.now() - order.created_at
                
                if order_age > timedelta(minutes=30):  # Cancel orders older than 30 minutes
                    self.log_warning(f"Cancelling stale order {order.order_id} (age: {order_age})")
                    await self._cancel_order(order)
            
        except Exception as e:
            self.log_error(f"Failed to monitor active orders: {e}")
    
    async def _cancel_order(self, order: Order):
        """Cancel an active order"""
        try:
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.now()
            
            # Move to completed orders
            self.completed_orders[order.order_id] = order
            if order.order_id in self.active_orders:
                del self.active_orders[order.order_id]
            
            # Update statistics
            self.execution_stats['orders_cancelled'] += 1
            
            self.log_info(f"Cancelled order {order.order_id} for {order.symbol}")
            
        except Exception as e:
            self.log_error(f"Failed to cancel order: {e}")
    
    async def _update_positions(self):
        """Update position information"""
        try:
            if self.trading_mode == TradingMode.PAPER and self.paper_account:
                # Get positions from paper account summary
                positions_summary = self.paper_account.get_positions_summary()
                
                # Clear existing positions and update from summary
                self.positions.clear()
                for position_data in positions_summary:
                    symbol = position_data['symbol']
                    self.positions[symbol] = position_data # Store the dictionary directly
                    
                    # Publish position update
                    await self.event_bus.publish(
                        EventTypes.POSITION_UPDATE,
                        {
                            "symbol": symbol,
                            "position_data": position_data,
                            "timestamp": datetime.now()
                        },
                        source=self.name
                    )
            
        except Exception as e:
            self.log_error(f"Failed to update positions: {e}")
    
    async def _check_stop_loss_triggers(self):
        """Check for stop-loss triggers"""
        try:
            # This would check current market prices against stop-loss levels
            # and automatically place stop-loss orders
            pass
            
        except Exception as e:
            self.log_error(f"Failed to check stop-loss triggers: {e}")
    
    async def _handle_market_data_update(self, event: Event):
        """Handle market data update for position monitoring"""
        try:
            symbol = event.data.get('symbol')
            data_point = event.data.get('data_point')
            
            if symbol and data_point and symbol in self.positions:
                # Update position with current market price
                current_price = data_point.ltp
                
                # Calculate unrealized P&L
                position = self.positions[symbol]
                if position:
                    # This would calculate unrealized P&L based on current price
                    pass
            
            self.increment_message_count()
            
        except Exception as e:
            self.log_error(f"Failed to handle market data update: {e}")
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics"""
        try:
            return {
                **self.execution_stats,
                'active_orders': len(self.active_orders),
                'completed_orders': len(self.completed_orders),
                'active_positions': len(self.positions),
                'trading_mode': self.trading_mode.value,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Failed to get execution statistics: {e}")
            return {}
    
    async def stop(self):
        """Stop the execution agent"""
        try:
            self.log_info("Stopping Modern Execution Agent...")
            
            self.running = False
            
            # Cancel background task
            if self._order_task:
                self._order_task.cancel()
                try:
                    await self._order_task
                except asyncio.CancelledError:
                    pass
                self._order_task = None

            # Cancel all active orders
            for order in list(self.active_orders.values()):
                await self._cancel_order(order)

            self.log_info("Modern Execution Agent stopped")
            
        except Exception as e:
            self.log_error(f"Error stopping Modern Execution Agent: {e}")
