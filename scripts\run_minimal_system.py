#!/usr/bin/env python3
"""
MINIMAL TRADING SYSTEM - NO DOWNLOADS
Completely bypasses data download to avoid hanging issues.
"""

import asyncio
import logging
import argparse
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent

logger = logging.getLogger(__name__)

class MinimalTradingSystem:
    def __init__(self):
        self.event_bus = EventBus()
        self.config = type('Config', (), {
            'selected_stocks': ["RELIANCE", "TCS", "INFY"],
            'timeframes': ["1min"],
            'mode': 'paper'
        })()
        self.session_id = "minimal_test"

    async def start(self):
        logger.info("Starting minimal system...")
        await self.event_bus.start_processor()
        
        # Create agents but don't download data
        market_agent = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)
        signal_agent = CleanSignalAgent(self.event_bus, self.config, self.session_id)
        
        # Start agents
        await market_agent.startup()
        await signal_agent.startup()
        
        logger.info("System started successfully - no hanging!")
        
        # Run for 30 seconds then stop
        await asyncio.sleep(30)
        
        await market_agent.shutdown()
        await signal_agent.shutdown()
        await self.event_bus.stop_processor()
        
        logger.info("System stopped cleanly")

async def main():
    logging.basicConfig(level=logging.INFO)
    system = MinimalTradingSystem()
    await system.start()

if __name__ == "__main__":
    asyncio.run(main())