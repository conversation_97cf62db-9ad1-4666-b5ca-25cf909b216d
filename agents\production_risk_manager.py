#!/usr/bin/env python3
"""
Production-Ready Risk Management System
======================================

A comprehensive risk management system designed for production trading environments
with multiple layers of protection, circuit breakers, and emergency controls.

Features:
- Multi-layer risk controls (pre-trade, real-time, post-trade)
- Dynamic position sizing with Kelly Criterion and volatility adjustment
- Circuit breakers and kill switches
- Real-time drawdown monitoring
- Emergency stop mechanisms
- Portfolio heat mapping
- Risk-adjusted performance tracking
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
from pathlib import Path

# Import base classes
from .base_agent import BaseAgent
from utils.event_bus import EventBus, EventTypes
from utils.risk_models import (
    TradeRequest, RiskAssessment, Position, Portfolio,
    TradeDirection, ProductType, OrderType
)

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class SystemState(Enum):
    """System operational states"""
    NORMAL = "normal"
    CAUTION = "caution"
    WARNING = "warning"
    EMERGENCY = "emergency"
    SHUTDOWN = "shutdown"

@dataclass
class CircuitBreaker:
    """Circuit breaker configuration and state"""
    name: str
    threshold: float
    current_value: float = 0.0
    triggered: bool = False
    trigger_time: Optional[datetime] = None
    reset_time: Optional[datetime] = None
    trigger_count: int = 0
    cooldown_minutes: int = 30

@dataclass
class RiskMetrics:
    """Comprehensive risk metrics"""
    timestamp: datetime
    portfolio_value: float
    total_exposure: float
    leverage_ratio: float
    var_95: float  # Value at Risk 95%
    expected_shortfall: float
    max_drawdown: float
    current_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    beta: float
    correlation_to_market: float
    concentration_risk: float
    liquidity_risk: float

@dataclass
class EmergencyStop:
    """Emergency stop configuration"""
    name: str
    condition: str
    threshold: float
    action: str
    enabled: bool = True
    triggered: bool = False
    trigger_time: Optional[datetime] = None

class ProductionRiskManager(BaseAgent):
    """
    Production-Ready Risk Management System
    
    Provides comprehensive risk management with multiple layers of protection:
    1. Pre-trade risk filters
    2. Real-time position monitoring
    3. Dynamic position sizing
    4. Circuit breakers and kill switches
    5. Emergency stop mechanisms
    6. Portfolio optimization
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        super().__init__("ProductionRiskManager", event_bus, config, session_id)
        
        # Initialize risk parameters
        self._initialize_risk_parameters()
        
        # Initialize portfolio tracking
        self._initialize_portfolio()
        
        # Initialize circuit breakers
        self._initialize_circuit_breakers()
        
        # Initialize emergency stops
        self._initialize_emergency_stops()
        
        # Initialize monitoring
        self._initialize_monitoring()
        
        # State management
        self.system_state = SystemState.NORMAL
        self.last_risk_check = datetime.now()
        self.risk_metrics_history = []
        
        # Performance tracking
        self.performance_metrics = {
            'trades_evaluated': 0,
            'trades_approved': 0,
            'trades_rejected': 0,
            'circuit_breakers_triggered': 0,
            'emergency_stops_triggered': 0,
            'risk_warnings_issued': 0
        }
        
        logger.info(f"[INIT] {self.name} initialized with production-grade risk controls")
    
    def _initialize_risk_parameters(self):
        """Initialize risk management parameters"""
        try:
            # Capital allocation
            self.total_capital = getattr(self.config, 'initial_balance', 100000)
            self.available_capital = self.total_capital
            self.used_capital = 0.0
            
            # Risk limits
            self.max_portfolio_risk = 0.02  # 2% portfolio risk
            self.max_position_risk = 0.005  # 0.5% per position
            self.max_daily_loss = 0.03  # 3% daily loss limit
            self.max_drawdown = 0.10  # 10% maximum drawdown
            self.max_leverage = 2.0  # 2x maximum leverage
            
            # Position sizing parameters
            self.kelly_lookback_days = 30
            self.volatility_lookback_days = 20
            self.min_position_size = 0.001  # 0.1% minimum position
            self.max_position_size = 0.05   # 5% maximum position
            
            # Risk-reward requirements
            self.min_risk_reward_ratio = 1.5
            self.preferred_risk_reward_ratio = 2.0
            
            logger.info(f"[INIT] Risk parameters initialized - Max portfolio risk: {self.max_portfolio_risk:.1%}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize risk parameters: {e}")
            raise
    
    def _initialize_portfolio(self):
        """Initialize portfolio tracking"""
        try:
            self.portfolio = Portfolio(
                portfolio_id=f"prod_portfolio_{self.session_id}",
                name="Production Trading Portfolio",
                total_capital=self.total_capital,
                available_capital=self.total_capital,
                utilized_capital=0.0,
                reserved_capital=0.0
            )
            
            self.positions = {}
            self.closed_positions = []
            self.daily_pnl = 0.0
            self.unrealized_pnl = 0.0
            self.realized_pnl = 0.0
            
            # Track high water mark for drawdown calculation
            self.high_water_mark = self.total_capital
            self.current_drawdown = 0.0
            
            logger.info(f"[INIT] Portfolio initialized with capital: ₹{self.total_capital:,.2f}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize portfolio: {e}")
            raise
    
    def _initialize_circuit_breakers(self):
        """Initialize circuit breakers"""
        try:
            self.circuit_breakers = {
                'daily_loss': CircuitBreaker(
                    name='Daily Loss Limit',
                    threshold=self.max_daily_loss,
                    cooldown_minutes=60
                ),
                'drawdown': CircuitBreaker(
                    name='Maximum Drawdown',
                    threshold=self.max_drawdown,
                    cooldown_minutes=120
                ),
                'leverage': CircuitBreaker(
                    name='Leverage Limit',
                    threshold=self.max_leverage,
                    cooldown_minutes=30
                ),
                'consecutive_losses': CircuitBreaker(
                    name='Consecutive Losses',
                    threshold=5,  # 5 consecutive losses
                    cooldown_minutes=60
                ),
                'api_failures': CircuitBreaker(
                    name='API Failure Rate',
                    threshold=0.5,  # 50% failure rate
                    cooldown_minutes=15
                )
            }
            
            logger.info(f"[INIT] {len(self.circuit_breakers)} circuit breakers initialized")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize circuit breakers: {e}")
            raise
    
    def _initialize_emergency_stops(self):
        """Initialize emergency stop mechanisms"""
        try:
            self.emergency_stops = {
                'market_crash': EmergencyStop(
                    name='Market Crash Detection',
                    condition='market_drop_5min',
                    threshold=0.05,  # 5% market drop in 5 minutes
                    action='stop_all_trading'
                ),
                'flash_crash': EmergencyStop(
                    name='Flash Crash Detection',
                    condition='market_drop_1min',
                    threshold=0.03,  # 3% market drop in 1 minute
                    action='emergency_exit_all'
                ),
                'system_overload': EmergencyStop(
                    name='System Overload',
                    condition='cpu_memory_usage',
                    threshold=0.95,  # 95% system resource usage
                    action='pause_new_trades'
                ),
                'data_feed_failure': EmergencyStop(
                    name='Data Feed Failure',
                    condition='data_staleness',
                    threshold=300,  # 5 minutes stale data
                    action='stop_all_trading'
                )
            }
            
            logger.info(f"[INIT] {len(self.emergency_stops)} emergency stops initialized")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize emergency stops: {e}")
            raise
    
    def _initialize_monitoring(self):
        """Initialize monitoring systems"""
        try:
            self.monitoring_active = True
            self.monitoring_interval = 10  # seconds
            self.last_health_check = datetime.now()
            
            # Risk monitoring flags
            self.risk_monitoring_enabled = True
            self.position_monitoring_enabled = True
            self.performance_monitoring_enabled = True
            
            logger.info("[INIT] Monitoring systems initialized")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize monitoring: {e}")
            raise

    async def initialize(self):
        """Initialize the risk manager (required by BaseAgent)"""
        try:
            logger.info(f"[INIT] Initializing {self.name}")
            # Initialization is done in __init__, this is for BaseAgent compatibility
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize {self.name}: {e}")
            return False

    async def stop(self):
        """Stop the risk manager (required by BaseAgent)"""
        try:
            logger.info(f"[STOP] Stopping {self.name}")
            self.is_running = False
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop {self.name}: {e}")
            return False

    async def _get_historical_trades(self, symbol: str, strategy_name: str) -> List[Dict[str, Any]]:
        """Get historical trades for Kelly calculation (placeholder implementation)"""
        try:
            # Return mock historical trades for testing
            return [
                {'pnl': 100, 'win': True},
                {'pnl': -50, 'win': False},
                {'pnl': 150, 'win': True},
                {'pnl': -75, 'win': False},
                {'pnl': 200, 'win': True}
            ]
        except Exception as e:
            logger.error(f"[ERROR] Failed to get historical trades: {e}")
            return []

    async def _get_recent_prices(self, symbol: str) -> List[float]:
        """Get recent prices for volatility calculation (placeholder implementation)"""
        try:
            # Return mock price data for testing
            return [18000, 18050, 17980, 18100, 18020, 18150, 18080, 18200, 18120, 18180]
        except Exception as e:
            logger.error(f"[ERROR] Failed to get recent prices: {e}")
            return []

    async def start(self):
        """Start the production risk manager"""
        try:
            await super().start()

            # Start monitoring loops
            asyncio.create_task(self._risk_monitoring_loop())
            asyncio.create_task(self._circuit_breaker_monitoring_loop())
            asyncio.create_task(self._emergency_stop_monitoring_loop())
            asyncio.create_task(self._performance_monitoring_loop())

            # Subscribe to events
            await self._subscribe_to_events()

            logger.info(f"[START] {self.name} started with all monitoring systems active")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            raise

    async def _subscribe_to_events(self):
        """Subscribe to relevant events"""
        try:
            # Subscribe to trade requests
            await self.event_bus.subscribe(
                EventTypes.TRADE_REQUEST,
                self._handle_trade_request
            )

            # Subscribe to market data updates
            await self.event_bus.subscribe(
                EventTypes.LIVE_PRICE_UPDATE,
                self._handle_price_update
            )

            # Subscribe to position updates
            await self.event_bus.subscribe(
                EventTypes.POSITION_UPDATE,
                self._handle_position_update
            )

            # Subscribe to system events
            await self.event_bus.subscribe(
                EventTypes.SYSTEM_ERROR,
                self._handle_system_error
            )

            logger.info("[EVENTS] Subscribed to all relevant events")

        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to events: {e}")

    async def validate_trade_request(self, trade_request: TradeRequest) -> RiskAssessment:
        """
        Comprehensive trade validation with multiple risk layers

        Args:
            trade_request: The trade request to validate

        Returns:
            RiskAssessment: Detailed risk assessment with approval/rejection
        """
        try:
            self.performance_metrics['trades_evaluated'] += 1

            # Check system state
            if self.system_state in [SystemState.EMERGENCY, SystemState.SHUTDOWN]:
                return self._create_rejection(
                    trade_request,
                    f"System in {self.system_state.value} state - trading suspended"
                )

            # Pre-trade risk checks
            risk_checks = await self._run_pre_trade_checks(trade_request)
            if not risk_checks['passed']:
                return self._create_rejection(trade_request, risk_checks['reason'])

            # Calculate optimal position size
            position_size, risk_amount = await self._calculate_optimal_position_size(trade_request)
            if position_size <= 0:
                return self._create_rejection(trade_request, "Position size calculation failed")

            # Validate risk-reward ratio
            rr_ratio = await self._calculate_risk_reward_ratio(trade_request)
            if rr_ratio < self.min_risk_reward_ratio:
                return self._create_rejection(
                    trade_request,
                    f"Risk-reward ratio {rr_ratio:.2f} below minimum {self.min_risk_reward_ratio:.2f}"
                )

            # Portfolio impact analysis
            portfolio_impact = await self._analyze_portfolio_impact(trade_request, position_size)
            if not portfolio_impact['acceptable']:
                return self._create_rejection(trade_request, portfolio_impact['reason'])

            # Concentration risk check
            concentration_risk = await self._check_concentration_risk(trade_request)
            if concentration_risk > 0.3:  # 30% concentration limit
                return self._create_rejection(
                    trade_request,
                    f"Concentration risk {concentration_risk:.1%} exceeds 30% limit"
                )

            # Create approval
            self.performance_metrics['trades_approved'] += 1

            return RiskAssessment(
                symbol=trade_request.symbol,
                signal_id=trade_request.signal_id,
                risk_score=self._calculate_risk_score(trade_request),
                position_size=position_size,
                max_loss=risk_amount,
                risk_reward_ratio=rr_ratio,
                approved=True,
                rejection_reason="",
                timestamp=datetime.now(),
                additional_data={
                    'portfolio_impact': portfolio_impact,
                    'concentration_risk': concentration_risk,
                    'system_state': self.system_state.value
                }
            )

        except Exception as e:
            logger.error(f"[ERROR] Trade validation failed: {e}")
            self.performance_metrics['trades_rejected'] += 1
            return self._create_rejection(trade_request, f"Validation error: {e}")

    async def _run_pre_trade_checks(self, trade_request: TradeRequest) -> Dict[str, Any]:
        """Run comprehensive pre-trade risk checks"""
        try:
            checks = {
                'passed': True,
                'reason': '',
                'details': {}
            }

            # Check circuit breakers
            for name, breaker in self.circuit_breakers.items():
                if breaker.triggered:
                    checks['passed'] = False
                    checks['reason'] = f"Circuit breaker '{name}' is active"
                    return checks

            # Check available capital
            if self.available_capital < trade_request.capital_allocated:
                checks['passed'] = False
                checks['reason'] = f"Insufficient capital: ₹{self.available_capital:,.2f} < ₹{trade_request.capital_allocated:,.2f}"
                return checks

            # Check maximum positions
            if len(self.positions) >= 10:  # Maximum 10 positions
                checks['passed'] = False
                checks['reason'] = "Maximum position limit (10) reached"
                return checks

            # Check symbol-specific limits
            symbol_positions = sum(1 for pos in self.positions.values() if pos.symbol == trade_request.symbol)
            if symbol_positions >= 2:  # Maximum 2 positions per symbol
                checks['passed'] = False
                checks['reason'] = f"Maximum positions for {trade_request.symbol} reached (2)"
                return checks

            # Check daily loss limit
            if self.daily_pnl < -self.max_daily_loss * self.total_capital:
                checks['passed'] = False
                checks['reason'] = f"Daily loss limit exceeded: {self.daily_pnl:,.2f}"
                return checks

            # Check drawdown limit
            if self.current_drawdown > self.max_drawdown:
                checks['passed'] = False
                checks['reason'] = f"Drawdown limit exceeded: {self.current_drawdown:.1%}"
                return checks

            return checks

        except Exception as e:
            logger.error(f"[ERROR] Pre-trade checks failed: {e}")
            return {'passed': False, 'reason': f"Check error: {e}", 'details': {}}

    async def _calculate_optimal_position_size(self, trade_request: TradeRequest) -> Tuple[int, float]:
        """Calculate optimal position size using multiple methods"""
        try:
            # Method 1: Risk-based sizing
            risk_per_trade = self.total_capital * self.max_position_risk
            stop_distance = abs(trade_request.entry_price - trade_request.stop_loss)

            if stop_distance <= 0:
                return 0, 0.0

            risk_based_size = int(risk_per_trade / stop_distance)

            # Method 2: Kelly Criterion (if historical data available)
            kelly_size = await self._calculate_kelly_position_size(trade_request)

            # Method 3: Volatility-adjusted sizing
            volatility_size = await self._calculate_volatility_adjusted_size(trade_request)

            # Method 4: Capital allocation limit
            max_capital_size = int(trade_request.capital_allocated / trade_request.entry_price)

            # Take the minimum of all methods for conservative sizing
            position_size = min(
                risk_based_size,
                kelly_size if kelly_size > 0 else risk_based_size,
                volatility_size if volatility_size > 0 else risk_based_size,
                max_capital_size
            )

            # Apply minimum and maximum position size limits
            min_size = max(1, int(self.min_position_size * self.total_capital / trade_request.entry_price))
            max_size = int(self.max_position_size * self.total_capital / trade_request.entry_price)

            position_size = max(min_size, min(position_size, max_size))
            risk_amount = position_size * stop_distance

            logger.debug(f"[SIZING] {trade_request.symbol}: Risk={risk_based_size}, Kelly={kelly_size}, "
                        f"Vol={volatility_size}, Final={position_size}")

            return position_size, risk_amount

        except Exception as e:
            logger.error(f"[ERROR] Position size calculation failed: {e}")
            return 0, 0.0

    async def _calculate_kelly_position_size(self, trade_request: TradeRequest) -> int:
        """Calculate position size using Kelly Criterion"""
        try:
            # Get historical performance for this symbol/strategy
            historical_trades = await self._get_historical_trades(
                trade_request.symbol,
                trade_request.strategy_name,
                days=self.kelly_lookback_days
            )

            if len(historical_trades) < 10:  # Need minimum 10 trades
                return 0

            # Calculate win rate and average win/loss
            wins = [trade for trade in historical_trades if trade['pnl'] > 0]
            losses = [trade for trade in historical_trades if trade['pnl'] < 0]

            if len(losses) == 0:  # No losses - use conservative sizing
                return 0

            win_rate = len(wins) / len(historical_trades)
            avg_win = np.mean([trade['pnl'] for trade in wins]) if wins else 0
            avg_loss = abs(np.mean([trade['pnl'] for trade in losses]))

            # Kelly formula: f = (bp - q) / b
            # where b = odds received (avg_win/avg_loss), p = win_rate, q = 1-p
            if avg_loss == 0:
                return 0

            b = avg_win / avg_loss
            p = win_rate
            q = 1 - p

            kelly_fraction = (b * p - q) / b

            # Apply safety factor (use 25% of Kelly)
            kelly_fraction = max(0, min(kelly_fraction * 0.25, self.max_position_size))

            # Convert to position size
            kelly_capital = self.total_capital * kelly_fraction
            kelly_size = int(kelly_capital / trade_request.entry_price)

            logger.debug(f"[KELLY] {trade_request.symbol}: WinRate={win_rate:.2%}, "
                        f"AvgWin={avg_win:.2f}, AvgLoss={avg_loss:.2f}, "
                        f"Kelly={kelly_fraction:.3f}, Size={kelly_size}")

            return kelly_size

        except Exception as e:
            logger.error(f"[ERROR] Kelly calculation failed: {e}")
            return 0

    async def _calculate_volatility_adjusted_size(self, trade_request: TradeRequest) -> int:
        """Calculate position size adjusted for volatility"""
        try:
            # Get recent price data for volatility calculation
            price_data = await self._get_recent_prices(
                trade_request.symbol,
                days=self.volatility_lookback_days
            )

            if len(price_data) < 10:
                return 0

            # Calculate volatility (standard deviation of returns)
            returns = np.diff(np.log(price_data))
            volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility

            # Adjust position size inversely to volatility
            # Higher volatility = smaller position
            base_volatility = 0.20  # 20% base volatility
            volatility_adjustment = base_volatility / max(volatility, 0.05)  # Minimum 5% volatility

            # Apply adjustment to base position size
            base_size = int(self.max_position_size * 0.5 * self.total_capital / trade_request.entry_price)
            volatility_size = int(base_size * volatility_adjustment)

            logger.debug(f"[VOLATILITY] {trade_request.symbol}: Vol={volatility:.2%}, "
                        f"Adjustment={volatility_adjustment:.2f}, Size={volatility_size}")

            return volatility_size

        except Exception as e:
            logger.error(f"[ERROR] Volatility adjustment failed: {e}")
            return 0

    async def _calculate_risk_reward_ratio(self, trade_request: TradeRequest) -> float:
        """Calculate risk-reward ratio for the trade"""
        try:
            risk = abs(trade_request.entry_price - trade_request.stop_loss)
            reward = abs(trade_request.take_profit - trade_request.entry_price)

            if risk <= 0:
                return 0.0

            return reward / risk

        except Exception as e:
            logger.error(f"[ERROR] Risk-reward calculation failed: {e}")
            return 0.0

    async def _analyze_portfolio_impact(self, trade_request: TradeRequest, position_size: int) -> Dict[str, Any]:
        """Analyze the impact of the trade on portfolio"""
        try:
            # Calculate position value
            position_value = position_size * trade_request.entry_price

            # Calculate new portfolio exposure
            current_exposure = sum(pos.current_value for pos in self.positions.values())
            new_exposure = current_exposure + position_value

            # Calculate leverage
            new_leverage = new_exposure / self.total_capital

            # Check leverage limit
            if new_leverage > self.max_leverage:
                return {
                    'acceptable': False,
                    'reason': f"Leverage {new_leverage:.2f}x exceeds limit {self.max_leverage:.2f}x",
                    'leverage': new_leverage,
                    'exposure': new_exposure
                }

            # Check portfolio heat (total risk)
            position_risk = position_size * abs(trade_request.entry_price - trade_request.stop_loss)
            current_risk = sum(pos.risk_amount for pos in self.positions.values())
            new_total_risk = current_risk + position_risk

            portfolio_risk_pct = new_total_risk / self.total_capital

            if portfolio_risk_pct > self.max_portfolio_risk:
                return {
                    'acceptable': False,
                    'reason': f"Portfolio risk {portfolio_risk_pct:.1%} exceeds limit {self.max_portfolio_risk:.1%}",
                    'portfolio_risk': portfolio_risk_pct,
                    'position_risk': position_risk
                }

            return {
                'acceptable': True,
                'reason': 'Portfolio impact acceptable',
                'leverage': new_leverage,
                'exposure': new_exposure,
                'portfolio_risk': portfolio_risk_pct,
                'position_risk': position_risk
            }

        except Exception as e:
            logger.error(f"[ERROR] Portfolio impact analysis failed: {e}")
            return {
                'acceptable': False,
                'reason': f"Analysis error: {e}",
                'leverage': 0,
                'exposure': 0
            }

    async def _check_concentration_risk(self, trade_request: TradeRequest) -> float:
        """Check concentration risk for the symbol/sector"""
        try:
            # Calculate symbol concentration
            symbol_exposure = sum(
                pos.current_value for pos in self.positions.values()
                if pos.symbol == trade_request.symbol
            )

            position_value = trade_request.quantity * trade_request.entry_price
            new_symbol_exposure = symbol_exposure + position_value

            symbol_concentration = new_symbol_exposure / self.total_capital

            # TODO: Add sector concentration check when sector mapping is available

            return symbol_concentration

        except Exception as e:
            logger.error(f"[ERROR] Concentration risk check failed: {e}")
            return 1.0  # Return high risk on error

    def _calculate_risk_score(self, trade_request: TradeRequest) -> float:
        """Calculate overall risk score for the trade"""
        try:
            risk_factors = []

            # Risk-reward ratio factor
            rr_ratio = abs(trade_request.take_profit - trade_request.entry_price) / \
                      abs(trade_request.entry_price - trade_request.stop_loss)
            rr_score = min(1.0, rr_ratio / 3.0)  # Normalize to 0-1, 3:1 RR = 1.0
            risk_factors.append(1.0 - rr_score)  # Lower RR = higher risk

            # Position size factor
            position_value = trade_request.quantity * trade_request.entry_price
            size_ratio = position_value / self.total_capital
            size_score = min(1.0, size_ratio / self.max_position_size)
            risk_factors.append(size_score)

            # Portfolio exposure factor
            current_exposure = sum(pos.current_value for pos in self.positions.values())
            exposure_ratio = (current_exposure + position_value) / self.total_capital
            exposure_score = min(1.0, exposure_ratio / self.max_leverage)
            risk_factors.append(exposure_score)

            # Calculate weighted average
            risk_score = np.mean(risk_factors)

            return min(1.0, max(0.0, risk_score))

        except Exception as e:
            logger.error(f"[ERROR] Risk score calculation failed: {e}")
            return 1.0  # Return maximum risk on error

    def _create_rejection(self, trade_request: TradeRequest, reason: str) -> RiskAssessment:
        """Create a trade rejection assessment"""
        self.performance_metrics['trades_rejected'] += 1

        return RiskAssessment(
            symbol=trade_request.symbol,
            signal_id=trade_request.signal_id,
            risk_score=1.0,  # Maximum risk for rejected trades
            position_size=0,
            max_loss=0.0,
            risk_reward_ratio=0.0,
            approved=False,
            rejection_reason=reason,
            timestamp=datetime.now(),
            additional_data={'system_state': self.system_state.value}
        )

    async def _risk_monitoring_loop(self):
        """Continuous risk monitoring loop"""
        while self.is_running:
            try:
                await self._update_portfolio_metrics()
                await self._check_risk_limits()
                await self._update_drawdown()
                await self._check_circuit_breakers()

                self.last_risk_check = datetime.now()
                await asyncio.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"[ERROR] Risk monitoring loop failed: {e}")
                await asyncio.sleep(5)

    async def _circuit_breaker_monitoring_loop(self):
        """Monitor and manage circuit breakers"""
        while self.is_running:
            try:
                for name, breaker in self.circuit_breakers.items():
                    await self._update_circuit_breaker(name, breaker)

                    # Check for reset conditions
                    if breaker.triggered and breaker.reset_time and datetime.now() > breaker.reset_time:
                        await self._reset_circuit_breaker(name, breaker)

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"[ERROR] Circuit breaker monitoring failed: {e}")
                await asyncio.sleep(5)

    async def _emergency_stop_monitoring_loop(self):
        """Monitor emergency stop conditions"""
        while self.is_running:
            try:
                for name, stop in self.emergency_stops.items():
                    if stop.enabled and not stop.triggered:
                        await self._check_emergency_condition(name, stop)

                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"[ERROR] Emergency stop monitoring failed: {e}")
                await asyncio.sleep(5)

    async def _performance_monitoring_loop(self):
        """Monitor system performance and health"""
        while self.is_running:
            try:
                # Update performance metrics
                await self._update_performance_metrics()

                # Check system health
                await self._check_system_health()

                # Log performance summary
                if datetime.now().minute % 15 == 0:  # Every 15 minutes
                    await self._log_performance_summary()

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"[ERROR] Performance monitoring failed: {e}")
                await asyncio.sleep(5)

    async def _update_circuit_breaker(self, name: str, breaker: CircuitBreaker):
        """Update circuit breaker status"""
        try:
            current_value = 0.0

            if name == 'daily_loss':
                current_value = abs(self.daily_pnl) / self.total_capital
            elif name == 'drawdown':
                current_value = self.current_drawdown
            elif name == 'leverage':
                total_exposure = sum(pos.current_value for pos in self.positions.values())
                current_value = total_exposure / self.total_capital
            elif name == 'consecutive_losses':
                current_value = await self._get_consecutive_losses()
            elif name == 'api_failures':
                current_value = await self._get_api_failure_rate()

            breaker.current_value = current_value

            # Check if breaker should trigger
            if not breaker.triggered and current_value >= breaker.threshold:
                await self._trigger_circuit_breaker(name, breaker)

        except Exception as e:
            logger.error(f"[ERROR] Failed to update circuit breaker {name}: {e}")

    async def _trigger_circuit_breaker(self, name: str, breaker: CircuitBreaker):
        """Trigger a circuit breaker"""
        try:
            breaker.triggered = True
            breaker.trigger_time = datetime.now()
            breaker.reset_time = datetime.now() + timedelta(minutes=breaker.cooldown_minutes)
            breaker.trigger_count += 1

            self.performance_metrics['circuit_breakers_triggered'] += 1

            # Update system state
            if name in ['daily_loss', 'drawdown']:
                self.system_state = SystemState.EMERGENCY
            else:
                self.system_state = SystemState.WARNING

            # Send alert
            await self.event_bus.publish(
                EventTypes.SYSTEM_ALERT,
                {
                    'type': 'circuit_breaker_triggered',
                    'name': name,
                    'threshold': breaker.threshold,
                    'current_value': breaker.current_value,
                    'trigger_time': breaker.trigger_time.isoformat(),
                    'reset_time': breaker.reset_time.isoformat(),
                    'system_state': self.system_state.value
                }
            )

            logger.critical(f"[CIRCUIT BREAKER] {breaker.name} TRIGGERED - "
                          f"Value: {breaker.current_value:.3f}, "
                          f"Threshold: {breaker.threshold:.3f}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger circuit breaker {name}: {e}")

    async def _reset_circuit_breaker(self, name: str, breaker: CircuitBreaker):
        """Reset a circuit breaker after cooldown"""
        try:
            breaker.triggered = False
            breaker.trigger_time = None
            breaker.reset_time = None

            # Check if system state can be improved
            active_breakers = [b for b in self.circuit_breakers.values() if b.triggered]
            if not active_breakers:
                self.system_state = SystemState.NORMAL

            logger.info(f"[CIRCUIT BREAKER] {breaker.name} RESET after cooldown")

            # Send notification
            await self.event_bus.publish(
                EventTypes.SYSTEM_ALERT,
                {
                    'type': 'circuit_breaker_reset',
                    'name': name,
                    'system_state': self.system_state.value
                }
            )

        except Exception as e:
            logger.error(f"[ERROR] Failed to reset circuit breaker {name}: {e}")

    async def _check_emergency_condition(self, name: str, stop: EmergencyStop):
        """Check if emergency stop condition is met"""
        try:
            condition_met = False

            if stop.condition == 'market_drop_5min':
                # Check for 5% market drop in 5 minutes
                condition_met = await self._check_market_drop(300, stop.threshold)
            elif stop.condition == 'market_drop_1min':
                # Check for 3% market drop in 1 minute
                condition_met = await self._check_market_drop(60, stop.threshold)
            elif stop.condition == 'cpu_memory_usage':
                # Check system resource usage
                condition_met = await self._check_system_resources(stop.threshold)
            elif stop.condition == 'data_staleness':
                # Check data feed staleness
                condition_met = await self._check_data_staleness(stop.threshold)

            if condition_met:
                await self._trigger_emergency_stop(name, stop)

        except Exception as e:
            logger.error(f"[ERROR] Failed to check emergency condition {name}: {e}")

    async def _trigger_emergency_stop(self, name: str, stop: EmergencyStop):
        """Trigger an emergency stop"""
        try:
            stop.triggered = True
            stop.trigger_time = datetime.now()

            self.performance_metrics['emergency_stops_triggered'] += 1
            self.system_state = SystemState.EMERGENCY

            # Execute emergency action
            if stop.action == 'stop_all_trading':
                await self._stop_all_trading()
            elif stop.action == 'emergency_exit_all':
                await self._emergency_exit_all_positions()
            elif stop.action == 'pause_new_trades':
                await self._pause_new_trades()

            logger.critical(f"[EMERGENCY STOP] {stop.name} TRIGGERED - Action: {stop.action}")

            # Send critical alert
            await self.event_bus.publish(
                EventTypes.SYSTEM_ALERT,
                {
                    'type': 'emergency_stop_triggered',
                    'name': name,
                    'condition': stop.condition,
                    'threshold': stop.threshold,
                    'action': stop.action,
                    'trigger_time': stop.trigger_time.isoformat(),
                    'system_state': self.system_state.value
                }
            )

        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger emergency stop {name}: {e}")
