#!/usr/bin/env python3
"""
Test script to verify signal generation is working
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.clean_signal_agent import CleanSignalAgent, TradingSignal
from core.event_system import EventBus
import polars as pl

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockConfig:
    def __init__(self):
        self.selected_stocks = ["RELIANCE", "HDFCBANK"]

async def test_signal_generation():
    """Test signal generation with mock data"""
    logger.info("Starting signal generation test...")
    
    # Create event bus and signal agent
    event_bus = EventBus()
    config = MockConfig()
    signal_agent = CleanSignalAgent(event_bus, config, "test_session")
    
    # Initialize the agent
    await signal_agent.initialize()
    
    # Create mock historical data
    timestamps = []
    current_time = datetime.now() - timedelta(hours=2)
    for i in range(100):
        timestamps.append(current_time + timedelta(minutes=i))
    
    # Create extreme price data that should trigger signals
    base_price = 2500.0
    prices = []
    volumes = []
    
    for i in range(100):
        if i < 50:
            # First half: declining trend (should trigger oversold RSI)
            trend = -i * 2  # Strong downward trend
            noise = (i % 5 - 2) * 1  # Small noise
        else:
            # Second half: strong upward trend (should trigger MA crossover)
            trend = -100 + (i - 50) * 3  # Strong upward trend
            noise = (i % 5 - 2) * 1  # Small noise
        
        price = base_price + trend + noise
        prices.append(price)
        
        # High volume during trend changes
        if i > 45 and i < 55:
            volumes.append(5000 + (i % 10) * 500)  # High volume during reversal
        else:
            volumes.append(1000 + (i % 20) * 100)
    
    # Create DataFrame
    mock_data = pl.DataFrame({
        "timestamp": timestamps,
        "open": [p * 0.999 for p in prices],
        "high": [p * 1.002 for p in prices],
        "low": [p * 0.998 for p in prices],
        "close": prices,
        "volume": volumes
    })
    
    logger.info(f"Created mock data with {len(mock_data)} rows")
    logger.info(f"Price range: {min(prices):.2f} - {max(prices):.2f}")
    
    # Test RSI strategy directly
    logger.info("Testing RSI strategy...")
    try:
        signal = await signal_agent._rsi_strategy("RELIANCE", mock_data)
        if signal:
            logger.info(f"RSI strategy generated signal: {signal.signal_type} strength={signal.strength}")
        else:
            logger.info("RSI strategy generated no signal")
    except Exception as e:
        logger.error(f"Error in RSI strategy: {e}")
    
    # Test MA crossover strategy
    logger.info("Testing MA crossover strategy...")
    try:
        signal = await signal_agent._ma_crossover_strategy("RELIANCE", mock_data)
        if signal:
            logger.info(f"MA crossover strategy generated signal: {signal.signal_type} strength={signal.strength}")
        else:
            logger.info("MA crossover strategy generated no signal")
    except Exception as e:
        logger.error(f"Error in MA crossover strategy: {e}")
    
    # Test full signal generation
    logger.info("Testing full signal generation...")
    signal_agent.historical_data_cache["RELIANCE"] = mock_data
    
    # Check the min_signal_strength threshold
    logger.info(f"Min signal strength threshold: {signal_agent.min_signal_strength}")
    
    try:
        result = await signal_agent._generate_signals_for_symbol("RELIANCE")
        logger.info(f"Full signal generation result: {result}")
        
        # Test signal combination directly
        logger.info("Testing signal combination...")
        rsi_signal = await signal_agent._rsi_strategy("RELIANCE", mock_data)
        if rsi_signal:
            logger.info(f"RSI signal details: {rsi_signal.signal_type} strength={rsi_signal.strength} strategy={rsi_signal.strategy_name}")
            logger.info(f"Strategy weights: {signal_agent.strategies}")
            
            signals = [rsi_signal]
            
            # Calculate scores manually to debug
            sell_signals = [s for s in signals if s.signal_type == "SELL"]
            sell_score = sum(s.strength * signal_agent.strategies[s.strategy_name]['weight'] for s in sell_signals)
            logger.info(f"Sell score: {sell_score}")
            logger.info(f"Sell score > 0.3? {sell_score > 0.3}")
            
            combined = await signal_agent._combine_signals("RELIANCE", signals)
            if combined:
                logger.info(f"Combined signal: {combined.signal_type} strength={combined.strength}")
                logger.info(f"Meets threshold? {combined.strength >= signal_agent.min_signal_strength}")
            else:
                logger.info("Signal combination returned None")
        
    except Exception as e:
        logger.error(f"Error in full signal generation: {e}")

if __name__ == "__main__":
    asyncio.run(test_signal_generation())