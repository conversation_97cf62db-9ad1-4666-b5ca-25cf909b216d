import asyncio
import pytest

from core.event_system import EventBus, EventTypes
from agents.risk_management_agent import RiskManagementAgent
from agents.modern_execution_agent import ModernExecutionAgent


class DummyConfig:
    def __init__(self):
        self.mode = "paper"
        self.initial_balance = 100000
        self.risk_limits = {
            'max_position_size': 0.1,
            'max_daily_loss': 0.05,
            'max_drawdown': 0.15,
        }
        self.selected_stocks = []


@pytest.mark.asyncio
async def test_signal_risk_execution_end_to_end():
    event_bus = EventBus()
    await event_bus.start_processor()

    config = DummyConfig()

    risk_agent = RiskManagementAgent(event_bus, config, session_id="test")
    exec_agent = ModernExecutionAgent(event_bus, config, session_id="test")

    assert await risk_agent.initialize()
    assert await exec_agent.initialize()

    await risk_agent.start()
    await exec_agent.start()

    approvals = []
    trades = []

    # Listen to approval and trade events for verification
    event_bus.subscribe(EventTypes.SIGNAL_RISK_APPROVED, lambda e: approvals.append(e))
    event_bus.subscribe(EventTypes.TRADE_EXECUTED, lambda e: trades.append(e))

    class Signal:
        def __init__(self):
            self.symbol = "TEST-EQ"
            self.signal_type = "BUY"
            self.entry_price = 100.0
            self.stop_loss = 99.0  # small risk to satisfy max loss check
            self.target_price = 110.0
            self.strength = 0.9
            self.confidence = 0.9
            self.strategy_name = "unit-test"
            self.signal_id = "sig-1"

    # Publish a fake signal
    await event_bus.publish(
        EventTypes.SIGNAL_GENERATED,
        {"signal": Signal()},
        source="test"
    )

    # Allow time for processing
    await asyncio.sleep(1.5)

    # Validate that approval was published and trade executed
    assert len(approvals) >= 1, "Risk agent did not approve signal"

    # Execution is async; give a bit more time for order flow and monitoring
    await asyncio.sleep(1.5)

    assert len(trades) >= 1, "Execution agent did not execute trade on approval"

    # Cleanup
    await risk_agent.stop()
    await exec_agent.stop()
    await asyncio.sleep(0)  # allow background tasks to conclude
    await event_bus.stop_processor()

