#!/usr/bin/env python3
"""
MODERN EVENT SYSTEM
Clean event-driven architecture for trading system communication

Based on best practices from:
- Event-driven microservices architecture
- Asynchronous message passing
- Loose coupling between components
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
import json
import uuid

logger = logging.getLogger(__name__)

@dataclass
class Event:
    """Event data structure"""
    id: str
    type: str
    data: Dict[str, Any]
    source: str
    timestamp: datetime
    correlation_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary"""
        return {
            'id': self.id,
            'type': self.type,
            'data': self.data,
            'source': self.source,
            'timestamp': self.timestamp.isoformat(),
            'correlation_id': self.correlation_id,
            'metadata': self.metadata or {}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Event':
        """Create event from dictionary"""
        return cls(
            id=data['id'],
            type=data['type'],
            data=data['data'],
            source=data['source'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            correlation_id=data.get('correlation_id'),
            metadata=data.get('metadata', {})
        )

class EventBus:
    """
    Modern event bus with async support
    
    Features:
    - Async event handling
    - Event filtering and routing
    - Dead letter queue for failed events
    - Event replay capability
    - Performance monitoring
    """
    
    def __init__(self, max_queue_size: int = 10000):
        self.max_queue_size = max_queue_size
        
        # Event handling
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.event_queue = asyncio.Queue(maxsize=max_queue_size)
        self.dead_letter_queue = asyncio.Queue(maxsize=1000)
        
        # Event storage for replay
        self.event_history: List[Event] = []
        self.max_history_size = 10000
        
        # Processing state
        self.running = False
        self.processor_task = None
        
        # Statistics
        self.stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'subscribers_count': 0,
            'processing_errors': 0
        }
        
        logger.info("[INIT] Event bus initialized")
    
    def subscribe(self, event_type: str, handler: Callable, filter_func: Optional[Callable] = None):
        """Subscribe to events of a specific type"""
        try:
            # Wrap handler with filter if provided
            if filter_func:
                async def filtered_handler(event: Event):
                    if filter_func(event):
                        await handler(event)
            else:
                filtered_handler = handler
            
            self.subscribers[event_type].append(filtered_handler)
            self.stats['subscribers_count'] = sum(len(handlers) for handlers in self.subscribers.values())
            
            logger.debug(f"[SUBSCRIBE] {handler.__name__} subscribed to {event_type}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to {event_type}: {e}")
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """Unsubscribe from events"""
        try:
            if event_type in self.subscribers:
                self.subscribers[event_type] = [h for h in self.subscribers[event_type] if h != handler]
                if not self.subscribers[event_type]:
                    del self.subscribers[event_type]
                
                self.stats['subscribers_count'] = sum(len(handlers) for handlers in self.subscribers.values())
                logger.debug(f"[UNSUBSCRIBE] {handler.__name__} unsubscribed from {event_type}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe from {event_type}: {e}")
    
    async def publish(self, event_type: str, data: Dict[str, Any], source: str, 
                     correlation_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
        """Publish an event"""
        try:
            event = Event(
                id=str(uuid.uuid4()),
                type=event_type,
                data=data,
                source=source,
                timestamp=datetime.now(),
                correlation_id=correlation_id,
                metadata=metadata
            )
            
            # Add to queue for processing
            if self.event_queue.full():
                logger.warning(f"[WARN] Event queue full, dropping oldest event")
                try:
                    self.event_queue.get_nowait()
                except asyncio.QueueEmpty:
                    pass
            
            await self.event_queue.put(event)
            self.stats['events_published'] += 1
            
            # Store in history
            self.event_history.append(event)
            if len(self.event_history) > self.max_history_size:
                self.event_history = self.event_history[-self.max_history_size:]
            
            logger.debug(f"[PUBLISH] Event {event.type} published by {source}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to publish event {event_type}: {e}")
    
    async def start_processor(self):
        """Start the event processor"""
        try:
            if self.running:
                logger.warning("[WARN] Event processor already running")
                return
            
            self.running = True
            self.processor_task = asyncio.create_task(self._process_events())
            
            logger.info("[START] Event processor started")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start event processor: {e}")
    
    async def stop_processor(self):
        """Stop the event processor"""
        try:
            self.running = False
            
            if self.processor_task:
                self.processor_task.cancel()
                try:
                    await self.processor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("[STOP] Event processor stopped")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop event processor: {e}")
    
    async def _process_events(self):
        """Process events from the queue"""
        try:
            logger.info("[PROCESSOR] Event processor loop started")
            
            while self.running:
                try:
                    # Get event from queue with timeout
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                    
                    # Process event
                    await self._handle_event(event)
                    self.stats['events_processed'] += 1
                    
                except asyncio.TimeoutError:
                    # No events to process, continue
                    continue
                except Exception as e:
                    logger.error(f"[ERROR] Error processing event: {e}")
                    self.stats['processing_errors'] += 1
                    await asyncio.sleep(0.1)
            
            logger.info("[PROCESSOR] Event processor loop ended")
            
        except Exception as e:
            logger.error(f"[ERROR] Event processor failed: {e}")
    
    async def _handle_event(self, event: Event):
        """Handle a single event"""
        try:
            handlers = self.subscribers.get(event.type, [])
            
            if not handlers:
                logger.debug(f"[HANDLER] No handlers for event type: {event.type}")
                return
            
            # Execute all handlers concurrently
            tasks = []
            for handler in handlers:
                task = asyncio.create_task(self._execute_handler(handler, event))
                tasks.append(task)
            
            # Wait for all handlers to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check for failures
            failed_count = sum(1 for result in results if isinstance(result, Exception))
            if failed_count > 0:
                logger.warning(f"[HANDLER] {failed_count}/{len(handlers)} handlers failed for {event.type}")
                self.stats['events_failed'] += 1
                
                # Add to dead letter queue for retry
                if not self.dead_letter_queue.full():
                    await self.dead_letter_queue.put(event)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to handle event {event.type}: {e}")
            self.stats['events_failed'] += 1
    
    async def _execute_handler(self, handler: Callable, event: Event):
        """Execute a single event handler"""
        try:
            if asyncio.iscoroutinefunction(handler):
                await handler(event)
            else:
                handler(event)
                
        except Exception as e:
            logger.error(f"[ERROR] Handler {handler.__name__} failed for event {event.type}: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get event bus statistics"""
        return {
            'events_published': self.stats['events_published'],
            'events_processed': self.stats['events_processed'],
            'events_failed': self.stats['events_failed'],
            'subscribers_count': self.stats['subscribers_count'],
            'processing_errors': self.stats['processing_errors'],
            'queue_size': self.event_queue.qsize(),
            'dead_letter_queue_size': self.dead_letter_queue.qsize(),
            'history_size': len(self.event_history),
            'running': self.running
        }
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get event history"""
        try:
            events = self.event_history
            
            if event_type:
                events = [e for e in events if e.type == event_type]
            
            # Return most recent events
            recent_events = events[-limit:] if len(events) > limit else events
            
            return [event.to_dict() for event in recent_events]
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get event history: {e}")
            return []
    
    async def replay_events(self, event_type: Optional[str] = None, 
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None):
        """Replay events from history"""
        try:
            events_to_replay = self.event_history
            
            # Filter by event type
            if event_type:
                events_to_replay = [e for e in events_to_replay if e.type == event_type]
            
            # Filter by time range
            if start_time:
                events_to_replay = [e for e in events_to_replay if e.timestamp >= start_time]
            
            if end_time:
                events_to_replay = [e for e in events_to_replay if e.timestamp <= end_time]
            
            logger.info(f"[REPLAY] Replaying {len(events_to_replay)} events")
            
            # Replay events
            for event in events_to_replay:
                await self._handle_event(event)
                await asyncio.sleep(0.001)  # Small delay to prevent overwhelming
            
            logger.info(f"[REPLAY] Completed replaying {len(events_to_replay)} events")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to replay events: {e}")

class EventFilter:
    """Event filtering utilities"""
    
    @staticmethod
    def by_source(source: str) -> Callable[[Event], bool]:
        """Filter events by source"""
        return lambda event: event.source == source
    
    @staticmethod
    def by_data_field(field: str, value: Any) -> Callable[[Event], bool]:
        """Filter events by data field value"""
        return lambda event: event.data.get(field) == value
    
    @staticmethod
    def by_time_range(start: datetime, end: datetime) -> Callable[[Event], bool]:
        """Filter events by time range"""
        return lambda event: start <= event.timestamp <= end
    
    @staticmethod
    def by_correlation_id(correlation_id: str) -> Callable[[Event], bool]:
        """Filter events by correlation ID"""
        return lambda event: event.correlation_id == correlation_id

# Event type constants
class EventTypes:
    """Standard event types for the trading system"""

    # Market data events
    MARKET_DATA_RECEIVED = "market_data_received"
    MARKET_DATA_UPDATE = "market_data_update"
    HISTORICAL_DATA_LOADED = "historical_data_loaded"
    LIVE_PRICE_UPDATE = "live_price_update"
    LIVE_CANDLE_UPDATE = "live_candle_update"
    WEBSOCKET_CONNECTED = "websocket_connected"
    WEBSOCKET_DISCONNECTED = "websocket_disconnected"

    # Trading signal events
    SIGNAL_GENERATED = "signal_generated"
    SIGNAL_VALIDATED = "signal_validated"
    SIGNAL_REJECTED = "signal_rejected"
    SIGNAL_RISK_APPROVED = "signal_risk_approved"

    # Risk management events
    RISK_CHECK_PASSED = "risk_check_passed"
    RISK_CHECK_FAILED = "risk_check_failed"
    POSITION_LIMIT_EXCEEDED = "position_limit_exceeded"
    HIGH_RISK_WARNING = "high_risk_warning"
    HIGH_EXPOSURE_WARNING = "high_exposure_warning"
    RISK_METRICS_UPDATE = "risk_metrics_update"

    # Execution events
    ORDER_PLACED = "order_placed"
    ORDER_FILLED = "order_filled"
    ORDER_REJECTED = "order_rejected"
    ORDER_CANCELLED = "order_cancelled"
    TRADE_EXECUTED = "trade_executed"
    POSITION_UPDATE = "position_update"

    # System events
    AGENT_STARTED = "agent_started"
    AGENT_STOPPED = "agent_stopped"
    AGENT_ERROR = "agent_error"
    SYSTEM_SHUTDOWN = "system_shutdown"