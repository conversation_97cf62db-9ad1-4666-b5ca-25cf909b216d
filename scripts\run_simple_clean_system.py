#!/usr/bin/env python3
"""
SIMPLE CLEAN TRADING SYSTEM
Simplified version that focuses on core functionality with 5 stocks only.
"""

import asyncio
import logging
import argparse
import signal
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / '.env')

from core.event_system import EventBus, EventTypes
from agents.clean_market_data_agent import CleanMarketDataAgent
from agents.clean_signal_agent import CleanSignalAgent
from agents.modern_execution_agent import ModernExecutionAgent

logger = logging.getLogger(__name__)

class SimpleConfig:
    """Simple configuration for the trading system."""
    def __init__(self, mode: str):
        self.mode = mode
        self.selected_stocks = ["RELIANCE", "HDFCBANK", "TCS", "INFY", "ICICIBANK"]
        self.timeframes = ["1min", "3min", "5min", "15min"]
        self.initial_balance = 1000000
        self.commission_rate = 0.0003
        self.max_position_size = 0.1
        self.risk_per_trade = 0.02

class SimpleCleanTradingSystem:
    """Simplified Clean Trading System."""
    
    def __init__(self, mode: str):
        self.mode = mode
        self.running = False
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Initialize components
        self.event_bus = EventBus()
        self.config = SimpleConfig(mode)
        self.agents = {}
        
        logger.info(f"🚀 Simple Clean Trading System initialized in {mode} mode")
        logger.info(f"📈 Selected stocks: {self.config.selected_stocks}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"[SIGNAL] Received signal {signum}, initiating shutdown...")
            self.running = False
        
        if os.name != 'nt':  # Unix systems
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        else:
            logger.info("[SIGNAL] Windows platform detected - KeyboardInterrupt will be handled in main loop")
    
    async def start(self):
        """Start the trading system."""
        try:
            logger.info("🎬 Starting Simple Clean Trading System...")
            
            # Start event bus
            await self.event_bus.start_processor()
            
            # Initialize agents
            await self._initialize_agents()
            
            # Start agents
            await self._start_agents()
            
            # Main monitoring loop
            await self._run_monitoring_loop()
            
        except Exception as e:
            logger.error(f"❌ System startup failed: {e}", exc_info=True)
            await self.stop()
    
    async def _initialize_agents(self):
        """Initialize all agents."""
        try:
            logger.info("🤖 Initializing agents...")
            
            # Market Data Agent
            self.agents['market_data'] = CleanMarketDataAgent(self.event_bus, self.config, self.session_id)
            await self.agents['market_data'].startup()

            # Signal Generation Agent
            self.agents['signal_generation'] = CleanSignalAgent(self.event_bus, self.config, self.session_id)
            await self.agents['signal_generation'].startup()

            # Execution Agent (best-effort start)
            self.agents['execution'] = ModernExecutionAgent(self.event_bus, self.config, self.session_id)
            try:
                # Prefer startup() if available, else initialize()+start()
                if hasattr(self.agents['execution'], 'startup'):
                    await self.agents['execution'].startup()
                else:
                    ok = await self.agents['execution'].initialize()
                    if ok and hasattr(self.agents['execution'], 'start'):
                        await self.agents['execution'].start()
            except Exception as e:
                logger.warning(f"[WARN] Execution agent failed to start cleanly: {e}")
            
            logger.info("✅ All agents initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Agent initialization failed: {e}")
            raise
    
    async def _start_agents(self):
        """Start all agents."""
        try:
            logger.info("🚀 Starting agents...")
            
            # Start agents in order
            for agent_name, agent in self.agents.items():
                logger.info(f"🔄 Starting {agent_name} agent...")
                await agent.startup()
                logger.info(f"✅ {agent_name} agent started")
            
            # Broadcast universe to all agents
            await self.event_bus.publish(
                "STOCK_UNIVERSE_UPDATED", 
                {'universe': self.config.selected_stocks}, 
                source="SimpleSystem"
            )
            
            self.running = True
            logger.info("🎉 All agents started successfully!")
            
        except Exception as e:
            logger.error(f"❌ Agent startup failed: {e}")
            raise
    
    async def _run_monitoring_loop(self):
        """Run the main monitoring loop."""
        try:
            logger.info("📊 Starting monitoring loop...")
            loop_count = 0
            
            while self.running:
                loop_count += 1
                
                # Health check every 60 seconds
                if loop_count % 12 == 0:  # Every 60 seconds (5s * 12)
                    await self._health_check()
                
                # Status update every 30 seconds
                if loop_count % 6 == 0:  # Every 30 seconds (5s * 6)
                    await self._status_update()
                
                await asyncio.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("⏹️ Monitoring interrupted by user")
            self.running = False
        except Exception as e:
            logger.error(f"❌ Monitoring loop error: {e}")
            self.running = False
    
    async def _health_check(self):
        """Perform health check on all agents."""
        try:
            logger.info("🏥 Performing health check...")
            
            for agent_name, agent in self.agents.items():
                if hasattr(agent, 'health_check'):
                    health = await agent.health_check()
                    status = "🟢 Healthy" if health.get('healthy', False) else "🔴 Unhealthy"
                    logger.info(f"   {agent_name}: {status}")
                else:
                    status = "🟢 Running" if agent.running else "🔴 Stopped"
                    logger.info(f"   {agent_name}: {status}")
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
    
    async def _status_update(self):
        """Print status update."""
        try:
            current_time = datetime.now().strftime('%H:%M:%S')
            logger.info(f"📊 Status Update - {current_time}")
            logger.info(f"   🎯 Mode: {self.mode}")
            logger.info(f"   📈 Stocks: {len(self.config.selected_stocks)}")
            logger.info(f"   🤖 Agents: {len(self.agents)} running")
            
            # Market data status
            if 'market_data' in self.agents:
                market_agent = self.agents['market_data']
                if hasattr(market_agent, 'live_candle_aggregators'):
                    active_symbols = len(getattr(market_agent, 'live_candle_aggregators', {}))
                    logger.info(f"   📡 Live streams: {active_symbols}")
            
        except Exception as e:
            logger.error(f"❌ Status update failed: {e}")
    
    async def stop(self):
        """Stop the trading system."""
        try:
            logger.info("🛑 Stopping Simple Clean Trading System...")
            self.running = False
            
            # Stop agents
            for agent_name, agent in self.agents.items():
                try:
                    logger.info(f"🔄 Stopping {agent_name} agent...")
                    await agent.stop()
                    logger.info(f"✅ {agent_name} agent stopped")
                except Exception as e:
                    logger.error(f"❌ Error stopping {agent_name}: {e}")
            
            # Stop event bus
            await self.event_bus.stop_processor()
            
            logger.info("🏁 System shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Shutdown error: {e}")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Simple Clean Trading System')
    parser.add_argument('--mode', choices=['paper', 'live'], default='paper', help='Trading mode')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Logging level')
    args = parser.parse_args()

    # Setup logging
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=args.log_level.upper(),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_dir / 'simple_clean_trading_system.log')
        ]
    )

    # Create and run system
    trading_system = SimpleCleanTradingSystem(args.mode)
    trading_system._setup_signal_handlers()

    try:
        await trading_system.start()
    except KeyboardInterrupt:
        logger.info("⏹️ System interrupted by user")
    except Exception as e:
        logger.error(f"❌ System error: {e}", exc_info=True)
    finally:
        if trading_system.running:
            await trading_system.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🏁 System shutdown complete")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}", exc_info=True)