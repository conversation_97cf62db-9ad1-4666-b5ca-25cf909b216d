#!/usr/bin/env python3
"""
🗄️ Live Data Management Agent
Handles downloading and managing historical OHLCV data for live stock selection workflow

Features:
- Downloads 25 days of historical data for stock lists
- Saves data in data/live/ directory with proper format
- Handles API rate limits and data quality validation
- Integrates with existing SmartAPI infrastructure
- Robust error handling and fallback mechanisms
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import yaml
import json
import time

# Import existing infrastructure
from core.smartapi_client import ModernSmartAPIClient, SmartAPICredentials, HistoricalDataRequest
from utils.instrument_master import InstrumentMaster
from utils.config_loader import load_config_for_agent
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class DataQualityMetrics:
    """Data quality metrics for validation"""
    symbol: str
    total_points: int
    missing_points: int
    missing_percentage: float
    outliers_detected: int
    data_completeness: float
    date_range_coverage: float
    is_valid: bool
    quality_score: float

class LiveDataManagementAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for downloading and managing historical data for live stock selection
    """
    
    def __init__(self, event_bus: Any, session_id: str):
        super().__init__("LiveDataManagementAgent", event_bus, None, session_id) # Pass None for config
        self.config = load_config_for_agent("live_data_management_agent")
        self.data_config = self.config['data_management']
        self.quality_config = self.data_config['data_quality']
        
        # Initialize components
        self.smartapi_client: Optional[ModernSmartAPIClient] = None
        self.instrument_master: Optional[InstrumentMaster] = None
        
        # Data storage
        self.downloaded_data: Dict[str, pl.DataFrame] = {}
        self.quality_metrics: Dict[str, DataQualityMetrics] = {}
        
        # Rate limiting
        self.last_request_time = 0
        self.request_count = 0
        self.request_window_start = time.time()
        
        # Concurrency control
        self.semaphore = asyncio.Semaphore(self.data_config.get('max_concurrent_requests', 10))
        
    async def _process_symbol_with_semaphore(self, symbol: str) -> bool:
        """Process a single symbol with semaphore control"""
        async with self.semaphore:
            try:
                # Apply rate limiting
                await self._apply_rate_limiting()
                
                # Download data for symbol
                data = await self._download_symbol_data(symbol)
                
                if data is not None and not data.is_empty():
                    # Validate data quality
                    quality_metrics = self._validate_data_quality(symbol, data)
                    self.quality_metrics[symbol] = quality_metrics
                    
                    if quality_metrics.is_valid:
                        # Save data
                        await self._save_symbol_data(symbol, data)
                        self.downloaded_data[symbol] = data
                        return True
                    else:
                        self.log_warning(f"⚠️ Data quality validation failed for {symbol}")
                        return False
                else:
                    self.log_warning(f"❌ No data received for {symbol}")
                    return False
                    
            except Exception as e:
                self.log_error(f"❌ Failed to process {symbol}: {e}")
                return False
    
    async def initialize(self) -> bool:
        """Initialize the agent with required components"""
        self.log_info("🗄️ Initializing Live Data Management Agent...")
        try:
            # Setup directories
            data_dir = Path(self.data_config['data_directory'])
            data_dir.mkdir(parents=True, exist_ok=True)
            (data_dir / "raw").mkdir(exist_ok=True)
            (data_dir / "processed").mkdir(exist_ok=True)
            (data_dir / "quality_reports").mkdir(exist_ok=True)
            self.log_info("✓ Data directories set up")

            # Initialize SmartAPI client
            try:
                smartapi_credentials = self.config['smartapi']
                credentials = SmartAPICredentials(
                    api_key=smartapi_credentials['api_key'],
                    username=smartapi_credentials['username'],
                    password=smartapi_credentials['password'],
                    totp_token=smartapi_credentials['totp_token']
                )
                self.smartapi_client = ModernSmartAPIClient(credentials=credentials)
                await self.smartapi_client.authenticate() # Use authenticate instead of initialize
                self.log_info("✓ SmartAPI client initialized and authenticated")
            except Exception as e:
                self.log_warning(f"SmartAPI client initialization or authentication failed: {e}")
                self.smartapi_client = None
                
            # Initialize instrument master
            try:
                self.instrument_master = InstrumentMaster()
                if self.instrument_master.load_instruments():
                    self.log_info("✓ Instrument master initialized")
                else:
                    self.log_warning("Instrument master failed to load instruments.")
                    self.instrument_master = None
            except Exception as e:
                self.log_warning(f"Instrument master initialization failed: {e}")
                self.instrument_master = None
                
            # Initialize semaphore after config is loaded
            self.semaphore = asyncio.Semaphore(self.data_config.get('max_concurrent_requests', 10))
            
            self.initialized = True
            self.log_info("✓ Live Data Management Agent initialized")
            return True
            
        except Exception as e:
            self.log_error(f"Failed to initialize Live Data Management Agent: {e}")
            self.initialized = False
            return False
            
    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live Data Management Agent started. Ready to download data on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live Data Management Agent...")
        try:
            if self.smartapi_client:
                await self.smartapi_client.cleanup()
            self.running = False
            self.log_info("🧹 Live Data Management Agent stopped and cleanup completed")
        except Exception as e:
            self.log_error(f"Failed to stop Live Data Management Agent: {e}")
            
    async def download_historical_data(self, stock_symbols: List[str]) -> Dict[str, pl.DataFrame]:
        """
        Download historical data for given stock symbols using batch processing
        
        Args:
            stock_symbols: List of stock symbols to download
            
        Returns:
            Dictionary mapping symbols to their data
        """
        self.log_info(f"📊 Starting historical data download for {len(stock_symbols)} stocks...")
        self.update_activity()
        
        # Filter excluded symbols
        excluded = set(self.data_config['stock_universe']['exclude_symbols'])
        stock_symbols = [s for s in stock_symbols if s not in excluded]
        
        # Filter to include_only if specified
        include_only = self.data_config['stock_universe']['include_only']
        if include_only:
            stock_symbols = [s for s in stock_symbols if s in include_only]
            
        self.log_info(f"Processing {len(stock_symbols)} stocks after filtering")

        # Process in batches for optimal performance
        batch_size = self.data_config.get('batch_size', 10)
        batches = [stock_symbols[i:i + batch_size] for i in range(0, len(stock_symbols), batch_size)]

        successful_downloads = 0
        failed_downloads = 0
        failed_symbols: List[str] = []

        for batch_idx, batch in enumerate(batches):
            self.log_info(f"📦 Processing batch {batch_idx + 1}/{len(batches)} ({len(batch)} symbols)")

            # Process batch concurrently
            tasks = [self._process_symbol_with_semaphore(symbol) for symbol in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for symbol, result in zip(batch, results):
                if isinstance(result, Exception):
                    self.log_error(f"❌ Failed to process {symbol}: {result}")
                    failed_downloads += 1
                    failed_symbols.append(symbol)
                elif result:
                    successful_downloads += 1
                    self.log_info(f"✓ Successfully processed {symbol}")
                else:
                    failed_downloads += 1
                    failed_symbols.append(symbol)

        # Retry pass for any failed symbols (single pass, reduced concurrency)
        if failed_symbols:
            self.log_info(f"🔁 Retrying {len(failed_symbols)} failed symbols with reduced concurrency...")
            retry_success = 0
            retry_failed: List[str] = []
            # Process retries sequentially to be conservative with API limits
            for symbol in failed_symbols:
                try:
                    ok = await self._process_symbol_with_semaphore(symbol)
                    if ok:
                        retry_success += 1
                        successful_downloads += 1
                        failed_downloads -= 1
                        self.log_info(f"✓ Retry successful for {symbol}")
                    else:
                        retry_failed.append(symbol)
                        self.log_warning(f"❌ Retry still failed for {symbol}")
                except Exception as e:
                    retry_failed.append(symbol)
                    self.log_error(f"❌ Retry error for {symbol}: {e}")
            if retry_failed:
                self.log_warning(f"⚠️ Still failed after retry: {len(retry_failed)} symbols (e.g., {retry_failed[:5]})")
            self.log_info(f"🔁 Retry pass complete: {retry_success} recovered")

        self.log_info(f"📊 Download complete: {successful_downloads} successful, {failed_downloads} failed")

        # Generate quality report
        await self._generate_quality_report()

        return self.downloaded_data

    async def _download_symbol_data(self, symbol: str) -> Optional[pl.DataFrame]:
        """Download historical data for a single symbol"""
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.data_config['historical_days'])
            
            # Try real data first
            if self.smartapi_client and self.instrument_master:
                data = await self._download_real_data(symbol, start_date, end_date)
                if data is not None and not data.is_empty():
                    return data
            
            # If real data download fails, log a warning and skip the symbol.
            self.log_warning(f"Could not retrieve real data for {symbol}. Skipping.")
            return None
            
        except Exception as e:
            self.log_error(f"Failed to download data for {symbol}: {e}")
            return None
            
    async def _download_real_data(self, symbol: str, start_date: datetime, end_date: datetime) -> Optional[pl.DataFrame]:
        """Download real data using SmartAPI"""
        try:
            # Get token for the symbol
            token = self.instrument_master.get_token(symbol, "NSE")
            if not token:
                self.log_warning(f"Token not found for {symbol}")
                return None
                
            # Download data using SmartAPI
            # The ModernSmartAPIClient's get_historical_data expects a HistoricalDataRequest object
            # which contains symbol_token, exchange, interval, from_date, to_date
            from core.smartapi_client import HistoricalDataRequest
            
            request = HistoricalDataRequest(
                symbol_token=token, # Use token as symbol_token
                exchange="NSE",
                interval="ONE_MINUTE",
                from_date=start_date,
                to_date=end_date
            )
            
            data = await self.smartapi_client.get_historical_data(request)
            
            if data and len(data) > 0:
                # SmartAPI returns data as arrays: [timestamp, open, high, low, close, volume]
                # Convert to proper DataFrame with column names
                try:
                    timestamps = [datetime.fromisoformat(row[0]) for row in data]
                    df = pl.DataFrame({
                        'timestamp': timestamps,
                        'open': [float(row[1]) for row in data],
                        'high': [float(row[2]) for row in data],
                        'low': [float(row[3]) for row in data],
                        'close': [float(row[4]) for row in data],
                        'volume': [int(row[5]) for row in data]
                    })
                    
                    self.log_info(f"✓ Retrieved {len(df)} records for {symbol}")
                    return df
                    
                except (IndexError, ValueError, TypeError) as e:
                    self.log_error(f"Data format error for {symbol}: {e}")
                    self.log_debug(f"Sample data: {data[:2] if len(data) >= 2 else data}")
                    return None
            else:
                self.log_warning(f"No data returned for {symbol}")
                return None
                
        except Exception as e:
            self.log_error(f"Real data download failed for {symbol}: {e}")
            return None
            
    def _create_demo_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pl.DataFrame:
        """Create demo data for testing purposes"""
        try:
            # Generate realistic demo data
            np.random.seed(hash(symbol) % 2**32)  # Consistent seed per symbol
            
            # Generate minute-by-minute data
            current_time = start_date
            data_points = []
            base_price = 100.0 + np.random.uniform(-50, 50)  # Random base price
            
            while current_time < end_date:
                # Skip weekends
                if current_time.weekday() < 5:  # Monday = 0, Friday = 4
                    # Market hours: 9:15 AM to 3:30 PM
                    if (9 <= current_time.hour < 15) or (current_time.hour == 15 and current_time.minute <= 30):
                        # Generate realistic price movement
                        price_change = np.random.normal(0, 0.001)  # Small random walk
                        base_price *= (1 + price_change)
                        
                        # Generate OHLC
                        open_price = base_price
                        high_price = base_price * (1 + abs(np.random.normal(0, 0.005)))
                        low_price = base_price * (1 - abs(np.random.normal(0, 0.005)))
                        close_price = base_price
                        volume = max(1, int(np.random.lognormal(8, 1)))  # Log-normal volume
                        
                        data_points.append({
                            'timestamp': current_time,
                            'open': round(open_price, 2),
                            'high': round(high_price, 2),
                            'low': round(low_price, 2),
                            'close': round(close_price, 2),
                            'volume': volume
                        })
                        
                current_time += timedelta(minutes=1)
                
            return pl.DataFrame(data_points)
            
        except Exception as e:
            self.log_error(f"Failed to create demo data for {symbol}: {e}")
            return pl.DataFrame()
            
    async def _apply_rate_limiting(self):
        """Apply API rate limiting with async lock for thread safety"""
        if not hasattr(self, '_rate_limit_lock'):
            self._rate_limit_lock = asyncio.Lock()
            
        async with self._rate_limit_lock:
            try:
                current_time = time.time()
                rate_config = self.data_config['api_rate_limit']
                
                # Reset window if needed
                if current_time - self.request_window_start >= 60:  # 1 minute window
                    self.request_count = 0
                    self.request_window_start = current_time
                    
                # Check per-minute limit with timeout to prevent hanging
                if self.request_count >= rate_config['requests_per_minute']:
                    sleep_time = min(60 - (current_time - self.request_window_start), 10.0)  # Max 10 second wait
                    if sleep_time > 0:
                        await asyncio.sleep(sleep_time)
                        self.request_count = 0
                        self.request_window_start = time.time()
                        
                # Check per-second limit with reduced delay
                time_since_last = current_time - self.last_request_time
                min_interval = max(1.0 / rate_config['requests_per_second'], 0.5)  # Minimum 0.5 second interval
                
                if time_since_last < min_interval:
                    sleep_time = min(min_interval - time_since_last, 2.0)  # Max 2 second wait
                    await asyncio.sleep(sleep_time)
                    
                self.last_request_time = time.time()
                self.request_count += 1
                
            except Exception as e:
                self.log_error(f"Rate limiting error: {e}")
                await asyncio.sleep(0.1)  # Shorter default delay for batch processing

    def _validate_data_quality(self, symbol: str, data: pl.DataFrame) -> DataQualityMetrics:
        """Validate data quality and generate metrics"""
        try:
            total_points = len(data)

            # Check for missing data
            missing_points = data.null_count().sum_horizontal()[0]
            missing_percentage = (missing_points / (total_points * len(data.columns))) * 100 if total_points > 0 else 100

            # Check data completeness
            min_required_points = self.quality_config['min_data_points']
            data_completeness = min(1.0, total_points / min_required_points) if min_required_points > 0 else 0

            # Check date range coverage
            if total_points > 0:
                expected_days = self.data_config['historical_days']
                actual_days = (data['timestamp'].max() - data['timestamp'].min()).days
                date_range_coverage = min(1.0, actual_days / expected_days) if expected_days > 0 else 0
            else:
                date_range_coverage = 0

            # Detect outliers
            outliers_detected = 0
            if self.quality_config['outlier_detection'] and total_points > 0:
                outliers_detected = self._detect_outliers(data)

            # Calculate quality score
            quality_score = self._calculate_quality_score(
                data_completeness, date_range_coverage, missing_percentage, outliers_detected, total_points
            )

            # Determine if data is valid
            is_valid = (
                total_points >= min_required_points and
                missing_percentage <= self.quality_config['max_missing_percentage'] and
                data_completeness >= 0.8 and
                date_range_coverage >= 0.7
            )

            return DataQualityMetrics(
                symbol=symbol,
                total_points=total_points,
                missing_points=missing_points,
                missing_percentage=missing_percentage,
                outliers_detected=outliers_detected,
                data_completeness=data_completeness,
                date_range_coverage=date_range_coverage,
                is_valid=is_valid,
                quality_score=quality_score
            )

        except Exception as e:
            self.log_error(f"Data quality validation failed for {symbol}: {e}")
            return DataQualityMetrics(
                symbol=symbol,
                total_points=0,
                missing_points=0,
                missing_percentage=100,
                outliers_detected=0,
                data_completeness=0,
                date_range_coverage=0,
                is_valid=False,
                quality_score=0
            )

    def _detect_outliers(self, data: pl.DataFrame) -> int:
        """Detect outliers in price data"""
        try:
            outlier_count = 0
            method = self.quality_config['outlier_method']
            threshold = self.quality_config['outlier_threshold']

            # Check price columns for outliers
            price_columns = ['open', 'high', 'low', 'close']

            for col in price_columns:
                if col in data.columns:
                    if method == "iqr":
                        q1 = data[col].quantile(0.25)
                        q3 = data[col].quantile(0.75)
                        iqr = q3 - q1
                        lower_bound = q1 - threshold * iqr
                        upper_bound = q3 + threshold * iqr
                        outliers = data.filter((pl.col(col) < lower_bound) | (pl.col(col) > upper_bound))
                        outlier_count += len(outliers)

                    elif method == "zscore":
                        mean_val = data[col].mean()
                        std_val = data[col].std()
                        if std_val > 0:
                            z_scores = (data[col] - mean_val) / std_val
                            outliers = data.filter(pl.col(col).map_elements(
                                lambda x: abs((x - mean_val) / std_val) > threshold
                            ))
                            outlier_count += len(outliers)

            return outlier_count

        except Exception as e:
            self.log_error(f"Outlier detection failed: {e}")
            return 0

    def _calculate_quality_score(self, data_completeness: float, date_range_coverage: float,
                                missing_percentage: float, outliers_detected: int, total_points: int) -> float:
        """Calculate overall quality score (0-1)"""
        try:
            # Weighted scoring
            completeness_score = data_completeness * 0.3
            coverage_score = date_range_coverage * 0.3
            missing_score = max(0, (100 - missing_percentage) / 100) * 0.2

            # Outlier penalty
            outlier_ratio = outliers_detected / total_points if total_points > 0 else 0
            outlier_score = max(0, 1 - outlier_ratio * 2) * 0.2  # Penalty for outliers

            total_score = completeness_score + coverage_score + missing_score + outlier_score
            return min(1.0, max(0.0, total_score))

        except Exception as e:
            self.log_error(f"Quality score calculation failed: {e}")
            return 0.0

    async def _save_symbol_data(self, symbol: str, data: pl.DataFrame):
        """Save symbol data to file asynchronously"""
        try:
            data_dir = Path(self.data_config['data_directory'])
            file_format = self.data_config['file_format']
            compression = self.data_config['compression']

            # Create filename
            filename = f"{symbol}_live_data.{file_format}"
            filepath = data_dir / filename

            # Save data in executor to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._save_data_sync, data, filepath, file_format, compression)

            self.log_debug(f"💾 Saved data for {symbol} to {filepath}")

        except Exception as e:
            self.log_error(f"Failed to save data for {symbol}: {e}")
    
    def _save_data_sync(self, data: pl.DataFrame, filepath: Path, file_format: str, compression: str):
        """Synchronous data saving helper"""
        if file_format == "parquet":
            data.write_parquet(filepath, compression=compression)
        elif file_format == "csv":
            data.write_csv(filepath)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")

    async def _generate_quality_report(self):
        """Generate data quality report"""
        try:
            if not self.quality_metrics:
                self.log_warning("No quality metrics to report")
                return

            # Aggregate metrics
            total_symbols = len(self.quality_metrics)
            valid_symbols = sum(1 for m in self.quality_metrics.values() if m.is_valid)
            avg_quality_score = np.mean([m.quality_score for m in self.quality_metrics.values()])
            avg_completeness = np.mean([m.data_completeness for m in self.quality_metrics.values()])

            # Create report
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_symbols_processed': total_symbols,
                    'valid_symbols': valid_symbols,
                    'invalid_symbols': total_symbols - valid_symbols,
                    'success_rate': valid_symbols / total_symbols if total_symbols > 0 else 0,
                    'average_quality_score': avg_quality_score,
                    'average_data_completeness': avg_completeness
                },
                'detailed_metrics': {
                    symbol: {
                        'total_points': metrics.total_points,
                        'missing_percentage': metrics.missing_percentage,
                        'data_completeness': metrics.data_completeness,
                        'date_range_coverage': metrics.date_range_coverage,
                        'outliers_detected': metrics.outliers_detected,
                        'quality_score': metrics.quality_score,
                        'is_valid': metrics.is_valid
                    }
                    for symbol, metrics in self.quality_metrics.items()
                }
            }

            # Save report
            report_dir = Path(self.data_config['data_directory']) / "quality_reports"
            report_file = report_dir / f"quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)

            self.log_info(f"📋 Quality report saved to {report_file}")
            self.log_info(f"📊 Quality Summary: {valid_symbols}/{total_symbols} valid symbols "
                       f"(success rate: {valid_symbols/total_symbols*100:.1f}%)")

        except Exception as e:
            self.log_error(f"Failed to generate quality report: {e}")

    def get_quality_metrics(self) -> Dict[str, DataQualityMetrics]:
        """Get quality metrics for all processed symbols"""
        return self.quality_metrics.copy()

    def get_downloaded_data(self) -> Dict[str, pl.DataFrame]:
        """Get all downloaded data"""
        return self.downloaded_data.copy()

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.smartapi_client:
                await self.smartapi_client.cleanup()

            self.log_info("🧹 Live Data Management Agent cleanup completed")

        except Exception as e:
            self.log_error(f"Cleanup failed: {e}")

# Example usage and testing
async def main():
    """Example usage of Live Data Management Agent"""
    try:
        # Initialize agent
        # agent = LiveDataManagementAgent() # Removed direct instantiation
        # await agent.initialize() # Removed direct initialization

        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveDataManagementAgent(event_bus, session_id)
        await agent.initialize()
        await agent.start() # Start the agent

        # Test with a few symbols
        test_symbols = ["RELIANCE", "TCS", "INFY", "HDFC", "ICICIBANK"]

        # Download data
        data = await agent.download_historical_data(test_symbols)

        # Print results
        print(f"\n📊 Downloaded data for {len(data)} symbols:")
        for symbol, df in data.items():
            print(f"  {symbol}: {len(df)} records")

        # Print quality metrics
        metrics = agent.get_quality_metrics()
        print(f"\n📋 Quality metrics:")
        for symbol, metric in metrics.items():
            print(f"  {symbol}: Quality Score = {metric.quality_score:.2f}, Valid = {metric.is_valid}")

        # Cleanup
        await agent.stop() # Stop the agent

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
